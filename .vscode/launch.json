{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "melodyze",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define=AWS_ACCESS_KEY_ID=********************",
                "--dart-define=AWS_SECRET_ACCESS_KEY=mOA2iv3CkhEbenDYOkYAltj3qGnDWPaVfJwl/rVf"
            ]
        },
        {
            "name": "melodyze (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            "args": [
                "--dart-define=AWS_ACCESS_KEY_ID=********************",
                "--dart-define=AWS_SECRET_ACCESS_KEY=mOA2iv3CkhEbenDYOkYAltj3qGnDWPaVfJwl/rVf"
            ]
        },
        {
            "name": "melodyze (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            "args": [
                "--dart-define=AWS_ACCESS_KEY_ID=********************",
                "--dart-define=AWS_SECRET_ACCESS_KEY=mOA2iv3CkhEbenDYOkYAltj3qGnDWPaVfJwl/rVf"
            ]
        },
        {
            "name": "melodyze (attach)",
            "request": "attach",
            "type": "dart"
        }
    ]
}
