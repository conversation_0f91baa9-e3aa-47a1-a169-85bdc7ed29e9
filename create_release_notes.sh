#!/bin/bash

# Check if GEMINI_API_KEY is set
if [ -z "$GEMINI_API_KEY" ]; then
    echo "Error: GEMINI_API_KEY environment variable is not set. Skipping API call."
else
    # Get the previous tag and release notes
    prev_tag=$(git for-each-ref --sort=-creatordate --format '%(objectname)' refs/tags | sed -n 2p)
    release_notes=$(git log --pretty=format:"- %s" "$prev_tag"..HEAD)

    # Convert multiline output to single line, remove trailing newline, and store in request variable
    request=$(echo "$release_notes" | tr '\n' ' ' | sed 's/- //' | sed 's/[[:space:]]*$//')

    # Call Gemini API
    response=$(curl -s \
        -H 'Content-Type: application/json' \
        -d "{\"contents\":[{\"parts\":[{\"text\":\"Highlihght top 4-5 features(max, can be less) only from following release notes. Don't give any description just the numeric points: $request\"}]}]}" \
        -X POST "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key=$GEMINI_API_KEY")

    # Extract the content text from the API response and write to release_notes.txt
    if [[ -n "$response" ]]; then # Check if response is not empty
        echo "$response" | jq -R '.' | jq -s '.' | jq -r 'join("  ")' | jq -r '.candidates[0].content.parts[0].text' >release_notes.txt
        echo "Release notes have been written to release_notes.txt"
    else
        echo "Error: Failed to get response from Gemini API."
    fi
fi

echo "Script execution complete."
