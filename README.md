# melodyze-android

### Run app

- Install melos
`dart pub global activate melos`

- Clean project
`melos clean`

- Install dependencies
`melos bootstrap`

- Dart Build
`dart run build_runner build`


### Change logo
1. Replace the image file here `assets/logo/logo.png`
2. Run `flutter pub run flutter_launcher_icons`


### Flutter fire configure

```
flutterfire configure --project=melodyze-65923 --android-package-name=ai.melodyze.app --android-app-id=ai.melodyze.app --ios-bundle-id=ai.melodyze.app
```


### iOS Configs
1. Need for JUCE API- 
xcode -> Build Settings -> Deployment
 `STRIP_STYLE = "non-global"`
