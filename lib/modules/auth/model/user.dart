import 'package:freezed_annotation/freezed_annotation.dart';

part 'user.freezed.dart';
part 'user.g.dart';

@freezed
class User with _$User {
  const factory User({
    @Json<PERSON>ey(name: '_id') required String id,
    @J<PERSON><PERSON><PERSON>(name: 'email_id') @Default('') String email,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'user_name') @Default('') String username,
    String? googlePhotoURL,
    @JsonKey(name: 'preferences') @Default(Preferences()) Preferences preferences,
  }) = _User;

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
}

@freezed
class Preferences with _$Preferences {
  const factory Preferences({
    @<PERSON><PERSON><PERSON><PERSON>(name: 'genres') @Default([]) List<String> genres,
    @JsonKey(name: 'artists') @Default([]) List<String> artists,
  }) = _Preferences;

  factory Preferences.fromJson(Map<String, dynamic> json) => _$PreferencesFrom<PERSON>son(json);
}
