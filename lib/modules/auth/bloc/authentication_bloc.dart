import 'dart:async';
import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/generic_models/repo_result.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/wrappers/analytics/crash_logger.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/event_bus.dart';
import 'package:melodyze/core/wrappers/firebase_cloud_messaging.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/modules/auth/auth_repository.dart';
import 'package:melodyze/modules/auth/bloc/authentication_event.dart';
import 'package:melodyze/modules/auth/bloc/authentication_state.dart';
import 'package:melodyze/modules/auth/model/user.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';

class AuthenticationBloc extends SafeBloc {
  final AuthRepository authRepository;
  User? user;
  final SecureStorageHelper _secureStorageHelper = DI().resolve<SecureStorageHelper>();
  final AccessTokenHelper _accessTokenHelper = DI().resolve<AccessTokenHelper>();
  final eventBus = DI().resolve<EventBus>();
  late final StreamSubscription _subscription;
  final FirstLoginHelper _firstLoginHelper = DI().resolve<FirstLoginHelper>();

  AuthenticationBloc({required this.authRepository}) : super(InitialState()) {
    _subscription = eventBus.on<SignOut>().listen((event) {
      add(SignOut());
    });

    on<GoogleLogin>(_onGoogleLogin);
    on<CheckAuth>(_onCheckAuth);
    on<SignOut>(_onSignOut);
    on<FirstLogin>(_onFirstLogin);

    add(CheckAuth());
  }

  @override
  Future<void> close() async {
    await _subscription.cancel();
    eventBus.dispose();
    return super.close();
  }

  FutureOr<void> _emitScreen() async {
    if ((await _accessTokenHelper.accessToken).isNullOrEmpty) {
      UserEvent.shared.login_loaded();
      emit(LoginScreenState());
    } else {
      emit(DashboardScreenState());
      add(FirstLogin());
    }
  }

  void _onGoogleLogin(GoogleLogin event, _) async {
    emit(LoadingState());
    try {
      final googleResponse = await authRepository.signInWithGoogle();
      if (!googleResponse.isSuccess) {
        emit(BlocFailureState(googleResponse.error));
        return;
      }
      //TODO: change this hacky return method
      final googleAuthToken = googleResponse.data![0]!;
      final response = await authRepository.melodyzeLogin(googleAuthToken: googleAuthToken);
      if (response.isSuccess) {
        await FirebaseCloudMessaging().updatedFcmToken();
        UserEvent.shared.login_success('google');
      } else {
        UserEvent.shared.login_login_failed('google', response.error.toString());
      }
      if (!response.isSuccess) {
        emit(BlocFailureState(response.error));
        return;
      }
      User? userResponse = response.data as User?;
      final userApiResponse = await authRepository.getProfile();
      if (userApiResponse.isSuccess) {
        userResponse = userApiResponse.data as User;
        DI().resolve<ProfileBloc>().add(const ProfileLoadDataEvent());
      }
      if (userResponse != null) {
        user = userResponse.copyWith(googlePhotoURL: googleResponse.data![1]);
        if (userResponse.preferences.artists.isNotEmpty || userResponse.preferences.genres.isNotEmpty) {
          await _secureStorageHelper.write(SecureStorageKeys.isPreferenceSelected, 'true');
        }
        await DI().resolve<CrashLogger>().setUserIdentifier(userResponse.id);
      }
      await _secureStorageHelper.write(SecureStorageKeys.user, jsonEncode(user?.toJson()));
      _emitScreen();
    } catch (e) {
      emit(BlocFailureState(RepoError('Error in signInWithGoogle', e)));
    }
  }

  Future<FutureOr<void>> _onCheckAuth(CheckAuth event, _) async {
    emit(LoadingState());
    try {
      final userData = await _secureStorageHelper.read(SecureStorageKeys.user);
      if (userData != null && userData.isNotEmpty) {
        user = User.fromJson(jsonDecode(userData));
        await FirebaseCloudMessaging().updatedFcmToken();
      }
    } catch (e) {
      logger.e(e.toString(), error: e);
    }
    _emitScreen();
  }

  Future<FutureOr<void>> _onSignOut(SignOut event, Emitter<BlocState> _) async {
    await authRepository.signOut();
    emit(const SignedOutState());
  }

  FutureOr<void> _onFirstLogin(FirstLogin event, _) async {
    await _firstLoginHelper.setFirstTime();
  }
}
