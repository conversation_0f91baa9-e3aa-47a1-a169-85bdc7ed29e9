import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_bottom_nav_bar/melo_bottom_nav_bar.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/auth/bloc/authentication_bloc.dart';
import 'package:melodyze/modules/auth/bloc/authentication_state.dart';
import 'package:melodyze/modules/feed/cubit/feed_cubit.dart';
import 'package:melodyze/modules/feed/repo/feed_repo.dart';
import 'package:melodyze/modules/feed/serivce/feed_service.dart';
import 'package:melodyze/modules/home/<USER>/home_bloc.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/home/<USER>/home_repo.dart';
import 'package:melodyze/modules/home/<USER>/home_service.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/share/cubit/fcm_notification_data_cubit.dart';

@RoutePage()
class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => DashboardScreenState();
}

class DashboardScreenState extends State<DashboardScreen> {
  final homeService = HomeService();
  final profileBloc = DI().resolve<ProfileBloc>();
  final feedCubit = FeedCubit(feedRepo: FeedRepo(feedService: FeedService()));

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notificationData = DI().resolve<FcmNotificationDataCubit>().notificationData;
      if (notificationData != null) {
        // If delay is no added then the player in feed also starts playing at the same time
        Future.delayed(const Duration(seconds: 1), () {
          DI().resolve<FcmNotificationDataCubit>().setNotificationData(notificationData);
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => HomeGridBloc(
            homeRepo: HomeRepo(
              homeService: homeService,
            ),
          ),
        ),
        BlocProvider(
          create: (context) => feedCubit,
        ),
        BlocProvider(
          create: (context) => HomeTilesBloc(
            homeRepo: HomeRepo(
              homeService: homeService,
            ),
          ),
        ),
        BlocProvider.value(value: profileBloc)
      ],
      child: BlocListener<AuthenticationBloc, BlocState>(
        listener: (context, state) {
          if (state is SignedOutState) {
            context.replaceRoute(LoginRoute());
          }
        },
        child: AutoTabsRouter(
          routes: [
            HomeRoute(),
            FeedRoute(),
            ProfileRoute(),
            SearchRoute(),
            VideoPlayerReelRoute(recordings: []),
          ],
          transitionBuilder: (context, child, animation) => FadeTransition(
            opacity: animation,
            child: child,
          ),
          builder: (context, child) {
            final tabsRouter = AutoTabsRouter.of(context);
            return MeloScaffold(
              showAppBar: false,
              extendBody: true,
              overlayAction: Positioned(
                bottom: 32,
                width: MediaQuery.sizeOf(context).width,
                child: MeloBottomNavigationBar(
                  currentIndex: tabsRouter.activeIndex,
                  items: const [AssetPaths.home, AssetPaths.feed, AssetPaths.profile],
                  onTap: (index) {
                    if (index == 1) {
                      context.read<FeedCubit>().loadPersonalizedFeed();
                    }
                    tabsRouter.setActiveIndex(index);
                  },
                ),
              ),
              body: BlocListener<FcmNotificationDataCubit, Map<String, dynamic>?>(
                listenWhen: (previous, current) => current != null,
                listener: (context, data) {
                  if (data != null) {
                    context.read<FcmNotificationDataCubit>().clearNotificationData();
                    context.pushRoute(
                      SongPersonalizationRoute(
                        song: SongModel(id: data["master_song_id"], defaultGenre: data["genre"] ?? '', title: data["title"] ?? '', singer: data["singer"] ?? ''),
                        defaultGenre: data["genre"] ?? '',
                      ),
                    );
                  }
                },
                child: child,
              ),
            );
          },
        ),
      ),
    );
  }
}
