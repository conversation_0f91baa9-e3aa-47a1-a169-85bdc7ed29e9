import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';

class ShareDataBloc extends SafeBloc<ShareDataEvent, ShareDataState> {
  AnnotatedData? annotatedData;
  SongModel? song;
  String? downloadedGuidePath;
  String? downloadedBgmPath;

  ShareDataBloc() : super(ShareDataState()) {
    on<ShareDataEvent>(((event, _) {
      annotatedData = event.annotatedData;
      song = event.song;
      downloadedGuidePath = event.guidePath;
      downloadedBgmPath = event.bgmPath;
      emit(ShareDataState());
    }));
  }
}

class ShareDataState extends BlocState {
  @override
  List<Object?> get props => [];
}

class ShareDataEvent extends BaseEvent {
  final AnnotatedData? annotatedData;
  final SongModel? song;
  final String? guidePath;
  final String? bgmPath;

  const ShareDataEvent({required this.annotatedData, required this.song, this.guidePath, this.bgmPath});

  @override
  List<Object?> get props => [annotatedData, song];
}
