import 'dart:async';
import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/services/aws_client.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/vocal_filters/vocal_filter_item.dart';

part 'vocal_filters_event.dart';
part 'vocal_filters_state.dart';

class VocalfiltersBloc extends SafeBloc<VocalFiltersEvent, VocalfiltersState> {
  final JuceMixBloc juceMixPlayerBloc;
  final ShareDataBloc shareDataBloc;
  final DownloadWavFileCubit downloadWavFileCubit;
  final String inputMic;
  final int timeDiff;
  final String recordedVocalPath;
  final String normalizedRecordingPath;
  String bgmWavPath = "";
  String recordedVocalS3path = "";

  String vocalFilterName = "None";
  String masterFilterName = "None";
  int selectedMixFilterIndex = 0;
  int selectedMasterFilterIndex = 0;
  double vocalVolume = 1.0;
  double musicVolume = 1.0;
  int delayFinal = 0;
  int userGivenDelay = 0;
  double vocalFromTime = 0;
  double vocalOffset = 0;
  double recDuration = 0;
  List<VocalFilterItem> _mixFilters = [];
  List<VocalFilterItem> _masterFilters = [];

  final Debouncer _debouncer = Debouncer();
  StreamSubscription<DownloadWavFileState>? _downloadWavFileListener;
  StreamSubscription<JuceMixState>? _vocalPlayerListener;

  // Seeking state tracking
  bool _wasPlayingBeforeSeek = false;
  bool _isUserPaused = false;

  VocalfiltersBloc({
    required this.recordedVocalPath,
    required this.normalizedRecordingPath,
    required this.juceMixPlayerBloc,
    required this.shareDataBloc,
    required this.downloadWavFileCubit,
    required this.inputMic,
    required this.timeDiff,
  }) : super(VocalFilterInitialState()) {
    on<VocalFiltersLoadEvent>(_loadVocalFiltersData);
    on<PlayEvent>(_onPlay);
    on<PauseEvent>(_onPause);
    on<StopEvent>(_onStop);
    on<SeekStartEvent>(_onSeekStart);
    on<SeekEndEvent>(_onSeekEnd);
    on<DelaySeekStartEvent>(_onDelaySeekStart);
    on<DelaySeekEndEvent>(_onDelaySeekEnd);
    on<VolumeSeekStartEvent>(_onVolumeSeekStart);
    on<VolumeSeekEndEvent>(_onVolumeSeekEnd);
    on<SelectMixFilterEvent>(_selectMixFilter);
    on<SelectMasterFilterEvent>(_selectMasterFilter);
    on<CheckWavFileDownloadStatus>(_checkWavFileDownloadStatus);
    on<MixFilterEditEvent>(_onMixFilterEdit);
    on<MasterFilterEditEvent>(_onMasterFilterEdit);
    add(VocalFiltersLoadEvent());
    add(CheckWavFileDownloadStatus());
  }

  /// Initialize delay values from local storage
  void _initializeDelayValues() {
    userGivenDelay = CommonUtils.getUserDelay();
    _calculateDelayOffset(userGivenDelay);
  }

  FutureOr<void> _initializeJuceMixComposeModel() async {
    try {
      recDuration = await JuceMixer().getAudioFileDuration(normalizedRecordingPath);
      juceMixPlayerBloc.outputDuration = recDuration;
      await _updateJuceMixComposeModel();

      juceMixPlayerBloc.add(JuceMixAudioInitEvent(autoPlay: true));
      emit(const VocalFilterLoadedState());
    } catch (e) {
      logger.e("Error initializing juceMixComposeModel: $e");
      emit(VocalFilterErrorState("Error initializing audio: ${e.toString()}"));
    }
  }

  @override
  Future<void> close() async {
    logger.d("VocalfiltersBloc closed");
    await _vocalPlayerListener?.cancel();
    await _downloadWavFileListener?.cancel();
    await CommonUtils.saveUserDelay(userGivenDelay);
    _debouncer.dispose();
    unawaited(super.close());
  }

  FutureOr<void> _checkWavFileDownloadStatus(CheckWavFileDownloadStatus event, _) async {
    if (downloadWavFileCubit.state is DownloadWavFileInProgressState) {
      emit(VocalFilterLoadingState());
      _downloadWavFileListener = downloadWavFileCubit.stream.firstWhere((state) => state is! DownloadWavFileInProgressState).asStream().listen((_) {
        onWavFileDownloadCompletion();
      });
    } else {
      onWavFileDownloadCompletion();
    }
  }

  void onWavFileDownloadCompletion() {
    if (downloadWavFileCubit.state is DownloadWavFileSuccessState) {
      bgmWavPath = downloadWavFileCubit.bgmWavPath!;
      logger.d("BGM wav file downloaded successfully: $bgmWavPath");
      _initializeJuceMixComposeModel();
    } else if (downloadWavFileCubit.state is DownloadWavFileErrorState) {
      emit(const VocalFilterErrorState("Failed to load BGM, Please try again later."));
    }
  }

  FutureOr<void> _loadVocalFiltersData(VocalFiltersLoadEvent event, _) async {
    emit(VocalFilterLoadingState());
    try {
      // Initialize delay values from local storage before _initializeJuceMixComposeModel
      _initializeDelayValues();

      // Initialize mix filters and master filters with genre sequence
      await getMixFilters();
      await getMasterFilters();

      unawaited(DI().resolve<AwsClient>().uploadFileByS3SignedUrl(recordedVocalPath, Endpoints.getUploadRawVocalCachedSignedUrl).then((s3Path) {
        if (s3Path == null || s3Path.isEmpty) {
          emit(VocalFilterErrorState("Unable upload recorded audio"));
          throw Exception("Audio upload error");
        } else {
          recordedVocalS3path = s3Path;
          logger.i("_uploadRawVocal: completed");
        }
      }));

      _vocalPlayerListener = juceMixPlayerBloc.stream.listen((state) {
        if (state is JuceMixAudioStopped) {
          add(StopEvent());
        }
      });

      // Initialize JuceMixComposeModel if bgmWavPath is already available
      // Otherwise, it will be initialized in onWavFileDownloadCompletion
      if (downloadWavFileCubit.state is DownloadWavFileSuccessState && downloadWavFileCubit.bgmWavPath != null) {
        bgmWavPath = downloadWavFileCubit.bgmWavPath!;
        await _initializeJuceMixComposeModel();
      }
    } catch (e) {
      logger.e(e.toString());
      emit(VocalFilterErrorState(e.toString()));
    }
  }

  FutureOr<void> _onPlay(PlayEvent event, _) async {
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioTogglePlayPauseEvent());
  }

  FutureOr<void> _onPause(PauseEvent event, _) async {
    _isUserPaused = true;
    juceMixPlayerBloc.add(JuceMixAudioTogglePlayPauseEvent());
  }

  FutureOr<void> _onStop(StopEvent event, _) async {
    juceMixPlayerBloc.add(JuceMixAudioStopEvent());
  }

  FutureOr<void> _onSeekStart(SeekStartEvent event, _) async {
    _wasPlayingBeforeSeek = juceMixPlayerBloc.isAudioPlaying;
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioPauseEvent());
  }

  FutureOr<void> _onSeekEnd(SeekEndEvent event, _) async {
    juceMixPlayerBloc.add(JuceMixAudioSeekEvent(milliseconds: event.milliseconds));
    if (_wasPlayingBeforeSeek && !_isUserPaused) {
      juceMixPlayerBloc.add(JuceMixAudioPlayEvent());
    }
  }

  FutureOr<void> _onVolumeSeekStart(VolumeSeekStartEvent event, _) async {
    _wasPlayingBeforeSeek = juceMixPlayerBloc.isAudioPlaying;
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioPauseEvent());
  }

  FutureOr<void> _onVolumeSeekEnd(VolumeSeekEndEvent event, _) async {
    if (event.isVocal) {
      vocalVolume = (event.value);
    } else {
      musicVolume = (event.value);
    }

    if (event.tabIndex == 0) {
      add(MixFilterEditEvent());
    } else if (event.tabIndex == 1) {
      add(MasterFilterEditEvent());
    }
    if (_wasPlayingBeforeSeek && !_isUserPaused) {
      juceMixPlayerBloc.add(JuceMixAudioPlayEvent());
    }
  }

  void _calculateDelayOffset(int delay) {
    userGivenDelay = delay;
    delayFinal = delay - timeDiff;
    if (delayFinal > 0) {
      vocalOffset = (vocalOffset + delayFinal.toDouble()).abs() / 1000;
      vocalFromTime = 0;
    } else if (delayFinal < 0) {
      vocalFromTime = (vocalFromTime + delayFinal.toDouble()).abs() / 1000;
      vocalOffset = 0;
    } else {
      vocalOffset = 0;
      vocalFromTime = 0;
    }
  }

  FutureOr<void> _onDelaySeekStart(DelaySeekStartEvent event, _) async {
    _wasPlayingBeforeSeek = juceMixPlayerBloc.isAudioPlaying;
    _isUserPaused = false;
    juceMixPlayerBloc.add(JuceMixAudioPauseEvent());
  }

  FutureOr<void> _onDelaySeekEnd(DelaySeekEndEvent event, _) async {
    _calculateDelayOffset(event.delay);
    add(MixFilterEditEvent());
    unawaited(CommonUtils.saveUserDelay(userGivenDelay));
    if (_wasPlayingBeforeSeek && !_isUserPaused) {
      juceMixPlayerBloc.add(JuceMixAudioPlayEvent());
    }
  }

  FutureOr<void> _selectMixFilter(SelectMixFilterEvent event, _) {
    selectedMixFilterIndex = event.selectedFilterIndex;
    juceMixPlayerBloc.onSetMixFilter(_mixFilters[selectedMixFilterIndex].id);
    emit(VocalFilterItemUpdated());
    add(MixFilterEditEvent());
    DI().resolve<AppToast>().showToast("${_mixFilters[selectedMixFilterIndex].title} applied");
    vocalFilterName = _mixFilters[selectedMixFilterIndex].title;
  }

  FutureOr<void> _selectMasterFilter(SelectMasterFilterEvent event, _) {
    selectedMasterFilterIndex = event.selectedFilterIndex;
    juceMixPlayerBloc.onSetMasterFilter(_masterFilters[selectedMasterFilterIndex].id);
    emit(VocalFilterItemUpdated());
    add(MasterFilterEditEvent());
    DI().resolve<AppToast>().showToast("${_masterFilters[selectedMasterFilterIndex].title} applied");
    masterFilterName = _masterFilters[selectedMasterFilterIndex].title;
  }

  Future<List<VocalFilterItem>> getMixFilters() async {
    const mixFilters = [
      ("NONE", "Normal", "normal"),
      ("AC_1", "Acoustic 1", "acoustic"),
      ("AC_2", "Acoustic 2", "acoustic"),
      ("Lofi_1", "LoFi 1", "lofi"),
      ("Lofi_2", "LoFi 2", "lofi"),
      ("Pop_1", "Pop 1", "pop"),
      ("Pop_2", "Pop 2", "pop"),
      ("Rock_1", "Rock 1", "rock"),
      ("Rock_2", "Rock 2", "rock"),
      ("Elec_1", "Electronic 1", "elec"),
    ];

    final filterItems = await _createFilterItemsWithGenreSequence(mixFilters);
    return _mixFilters = filterItems;
  }

  // Synchronous getter for UI - returns already loaded filters
  List<VocalFilterItem> get mixFilters => _mixFilters;

  Future<List<VocalFilterItem>> getMasterFilters() async {
    const List<(String, String, String)> masterFilters = [
      ("NONE", "Normal", "normal"),
      ("AC_MST", "Acoustic", "acoustic"),
      ("Pop_MST", "Pop", "pop"),
      ("Rock_MST", "Rock", "rock"),
      ("Lofi_MST", "LoFi", "lofi"),
      ("Elec_MST", "Electronic", "elec"),
    ];

    final filterItems = await _createFilterItemsWithGenreSequence(masterFilters);
    return _masterFilters = filterItems;
  }

  // Synchronous getter for UI - returns already loaded master filters
  List<VocalFilterItem> get masterFilters => _masterFilters;

  /// Common method to create filter items with genre sequence sorting
  Future<List<VocalFilterItem>> _createFilterItemsWithGenreSequence(
    List<(String, String, String)> filters,
  ) async {
    final genreMap = Map<String, String>.from(json.decode(await rootBundle.loadString(AssetPaths.genreMap)));
    final genreSequence = Map<String, List<dynamic>>.from(json.decode(await rootBundle.loadString(AssetPaths.genreSequence)));

    final songGenre = shareDataBloc.annotatedData?.genre ?? "";
    final genreKey = genreMap[songGenre] ?? "Elec";

    final sequenceOrder = genreSequence[genreKey]?.cast<String>();
    if (sequenceOrder == null || sequenceOrder.isEmpty) {
      throw Exception("Missing genre sequence for $genreKey");
    }

    final filterItems = filters
        .map((filter) => VocalFilterItem(
              id: filter.$1,
              title: filter.$2,
              thumbnailPath: "${AssetPaths.filterIcons}/${filter.$3}.png",
            ))
        .toList();

    String extractGenre(String id) {
      return id.split("_").firstWhere((_) => true, orElse: () => "Elec");
    }

    filterItems.sort((a, b) {
      if (a.id == "NONE") return -1;
      if (b.id == "NONE") return 1;

      final indexA = sequenceOrder.indexOf(extractGenre(a.id));
      final indexB = sequenceOrder.indexOf(extractGenre(b.id));

      return indexA.compareTo(indexB);
    });

    return filterItems;
  }

  FutureOr<void> _onMixFilterEdit(MixFilterEditEvent event, _) async {
    logger.d("onMixFilterEdit:");
    await _updateJuceMixComposeModel();
  }

  FutureOr<void> _onMasterFilterEdit(MasterFilterEditEvent event, _) async {
    logger.d("onMasterFilterEdit:");
    await _updateJuceMixComposeModel();
  }

  Future<void> _updateJuceMixComposeModel() async {
    try {
      if (bgmWavPath.isEmpty) {
        logger.d("bgmWavPath is not available, cannot update JuceMixComposeModel");
        throw Exception("BGM wav file not available");
      }

      final tempoString = shareDataBloc.annotatedData?.tempo;
      final parsedTempo = int.tryParse(tempoString ?? '');

      if (parsedTempo == null) {
        logger.e("updateJuceMixComposeModel: Invalid tempo: $tempoString");
        throw Exception("Invalid tempo configuration");
      }
      // Create updated juceMixComposeModel with current settings
      juceMixPlayerBloc.mixComposeModel = await JuceMixUtils.createComposeModel(
        outputDuration: recDuration,
        bgmPath: bgmWavPath,
        vocalPath: normalizedRecordingPath,
        bgmVol: musicVolume,
        vocalVol: vocalVolume,
        metronomeEnabled: juceMixPlayerBloc.isMetronomeEnabled,
        guideEnabled: juceMixPlayerBloc.isGuideEnabled,
        guidePath: shareDataBloc.downloadedGuidePath ?? "",
        guideVol: juceMixPlayerBloc.guideVol,
        metronomeVol: juceMixPlayerBloc.metronomeVol,
        timeSign: shareDataBloc.song!.timeSignature,
        tempo: parsedTempo,
      );

      // Update vocal track with offset and fromTime for delay handling
      final updatedTracks = juceMixPlayerBloc.mixComposeModel.tracks?.map((track) {
        if (track.id == "vocal") {
          return track.copyWith(
            volume: vocalVolume,
            offset: vocalOffset,
            fromTime: vocalFromTime,
          );
        } else if (track.id == "bgm") {
          return track.copyWith(volume: musicVolume);
        }
        return track;
      }).toList();

      juceMixPlayerBloc.mixComposeModel = juceMixPlayerBloc.mixComposeModel.copyWith(tracks: updatedTracks);
      juceMixPlayerBloc.setMixData();

      logger.d("Updated juceMixComposeModel with ${juceMixPlayerBloc.mixComposeModel.tracks?.length ?? 0} tracks");
    } catch (e) {
      logger.e("Error updating juceMixComposeModel: $e");
    }
  }
}






























  // Future<String> _getJuceDir({required bool isDenoise}) async {
  //   final documentsDirpath = await PathWrapper.getApplicationDocumentsDirectoryPath();
  //   return "$documentsDirpath/juce_out${isDenoise ? "_d" : ""}/";
  // }

  // List<VocalFilterItem> getFilters() {
  //   // Sort: Keep items with isLoading = true at the end
  //   _filters.sort((a, b) => a.isLoading == b.isLoading ? 0 : (a.isLoading ? 1 : -1));
  //   List<VocalFilterItem> list = _filters.where((f) => f.isDenoise == isDenoiseSelected).toList();
  //   return list;
  // }

  // List<MasterFilterItem> getMasterFilters() {
  //   List<MasterFilterItem> list = _masterFilters.toList();
  //   return list;
  // }

  // Future<String> _getMixWavOutputDirectory() async {
  //   final documentsDirpath = await PathWrapper.getApplicationDocumentsDirectoryPath();
  //   return "$documentsDirpath/mix_out";
  // }


  // void _hideControlsAfterDelay() {
  //   _hideTimer?.cancel();
  //   _hideTimer = Timer(_hideControlsDuration, () {
  //     add(HideControls());
  //   });
  // }


  // List<VocalFilterItem> _getFilters({required bool isDenoise}) {
  //   List<VocalFilterItem> list = _filters.where((f) => f.isDenoise == isDenoise).toList();
  //   return list;
  // }



  // VocalFilterItem? _getSelectedVocalFilter() {
  //   VocalFilterItem selectedFilter = getFilters()[selectedFilterIndex];
  //   vocalFilterName = selectedFilter.title;
  //   return selectedFilter;
  // }


  // Future<void> _mixWavFiles(FilterEditChanged event) async {
  //   logger.d("mixWavFiles $event");
  //   currentEvent = event;
  //   _deletePreviousMixFile();

  //   try {
  //     // Get selected filter and validate
  //     // final selectedFilter = _getSelectedVocalFilter();
  //     // if (selectedFilter?.fileURL == null || selectedFilter!.isLoading) {
  //     //   logger.e("_mixWavFiles: Selected filter file is null or still loading");
  //     //   emit(const VocalFilterPlayerErrorState("Vocal file not found!"));
  //     //   return;
  //     // }

  //     // Verify input file exists
  //     final normalizedVocalFile = File(normalizedRecordingPath);
  //     if (!normalizedVocalFile.existsSync()) {
  //       logger.e("mixWavFiles: Vocal file does not exist at path: $normalizedRecordingPath");
  //       emit(const VocalFilterPlayerErrorState("Vocal file not found!"));
  //       return;
  //     }

  //     logger.d("SELECTED Vocal File: $normalizedRecordingPath");
  //     emit(const VocalFilterLoadedState(isLoading: true));

  //     // Prepare output directory
  //     final dirPath = await _getMixWavOutputDirectory();
  //     final dir = Directory(dirPath);
  //     if (!dir.existsSync()) {
  //       await dir.create(recursive: true);
  //     }

  //     final juceFinalMixOutput = "$dirPath/${DateTime.now().millisecondsSinceEpoch}.wav";

  //     // Get audio file duration
  //     JuceMixer juceMixer = JuceMixer();
  //     final outDuration = await juceMixer.getAudioFileDuration(normalizedRecordingPath);
  //     logger.d("vocalOffset:$vocalOffset | vocalFromTime:$vocalFromTime");
  //     logger.d("outDuration: $outDuration");

  //     MixerComposeModel juceComposeModel = MixerComposeModel(
  //       tracks: [
  //         MixerTrack(
  //           id: MixerTrackType.vocal.createString(),
  //           path: normalizedRecordingPath,
  //           volume: vocalVolume,
  //           offset: vocalOffset,
  //           fromTime: vocalFromTime,
  //         ),
  //         MixerTrack(
  //           id: MixerTrackType.bgm.createString(),
  //           path: bgmWavPath,
  //           volume: musicVolume,
  //         ),
  //       ],
  //       output: juceFinalMixOutput,
  //       outputDuration: outDuration,
  //     );

  //     if (juceMixPlayerBloc.isMetronomeEnabled) {
  //       juceComposeModel.tracks?.addAll(juceMixPlayerBloc.mixComposeModel.metronomeTracks);
  //     }

  //     if (juceMixPlayerBloc.isGuideEnabled) {
  //       final guideTrack = juceMixPlayerBloc.mixComposeModel.tracks?.where((track) => track.id == MixerTrackType.guide.createString());
  //       juceComposeModel.tracks?.addAll(guideTrack ?? []);
  //     }

  //     juceMixPlayerBloc.mixComposeModel = juceComposeModel;

  //     for (int i = 0; i < (juceComposeModel.tracks?.length ?? 0); i++) {
  //       final track = juceComposeModel.tracks!.toList()[i];
  //       logger.d("JUCE MIX Compose tracks: ${track.id}: ${track.volume}: ${track.duration} : ${track.fromTime}: ${track.offset}");
  //     }

  //     if (event.isMasterFilter) {
  //       juceMixPlayerBloc.add(JuceSetMasterFilterEvent(masterFilterId: filtersMaster[selectedMasterFilterIndex]));
  //     } else {
  //       juceMixPlayerBloc.add(JuceSetVocalFilterEvent(vocalFilterId: filtersVocal[selectedFilterIndex]));
  //     }

  //     emit(const VocalFilterLoadedState(isLoading: false));
  //   } catch (e, stackTrace) {
  //     logger.e("_mixWavFiles error: $e\n$stackTrace");
  //     emit(VocalFilterPlayerErrorState("Error processing audio: ${e.toString()}"));
  //   }
  // }

  // Future<void> createFilterItems({required bool isDenoise}) async {
  //   const List<(String, String, String)> staticfileNames = [
  //     ("acoustic_gentle_male.wav", "Acoustic 1", "acoustic"),
  //     ("acoustic_aggressive_male.wav", "Acoustic 2", "acoustic"),
  //     ("lofi_gentle_male.wav", "LoFi 1", "lofi"),
  //     ("lofi_aggressive_male.wav", "LoFi 2", "lofi"),
  //     ("pop_gentle_male.wav", "Pop 1", "pop"),
  //     ("pop_aggressive_male.wav", "Pop 2", "pop"),
  //     ("rock_gentle_male.wav", "Rock 1", "rock"),
  //     ("rock_aggressive_male.wav", "Rock 2", "rock"),
  //     ("elec_aggressive_male.wav", "Electronic 1", "elec"),
  //   ];

  //   const genreMapPath = "assets/juce_configs/genre_map.json";
  //   final genreMapJson = await rootBundle.loadString(genreMapPath);
  //   final Map<String, String> genreMap = Map<String, String>.from(json.decode(genreMapJson));

  //   final genre = shareDataBloc.annotatedData!.genre;

  //   // Get the short code for the genre
  //   final shortCode = genreMap[genre] ?? genre;

  //   // Generate the fileNames list
  //   final List<(String, String, String)> fileNames = [];
  //   for (var i = 0; i < staticfileNames.length; i++) {
  //     var (fileName, title, alias) = staticfileNames[i];
  //     if (title.startsWith(shortCode)) {
  //       fileNames.add(staticfileNames[i]);
  //     }
  //   }

  //   for (var i = 0; i < staticfileNames.length; i++) {
  //     var (fileName, title, alias) = staticfileNames[i];
  //     if (!title.startsWith(shortCode)) {
  //       fileNames.add(staticfileNames[i]);
  //     }
  //   }

  //   String output = await _getJuceDir(isDenoise: isDenoise);
  //   _filters.add(VocalFilterItem(
  //       isDenoise: isDenoise,
  //       title: normalFilter,
  //       fileURL: isDenoise ? "/dummy.wav" : normalizedRecordingPath,
  //       thumbnailPath: "${AssetPaths.filterIcons}/normal.png",
  //       isLoading: isDenoise ? true : false));
  //   for (var i = 0; i < fileNames.length; i++) {
  //     var (fileName, title, alias) = fileNames[i];
  //     var item = VocalFilterItem(
  //       isDenoise: isDenoise,
  //       title: title,
  //       fileURL: "$output$fileName",
  //       thumbnailPath: "${AssetPaths.filterIcons}/$alias.png",
  //     );
  //     _filters.add(item);
  //   }
  // }

  // Future<void> createMasterFilterItems({required bool isDenoise}) async {
  //   const List<(String, String, String)> fileNames = [
  //     ("acoustic_master.wav", "Acoustic", "acoustic"),
  //     ("rock_master.wav", "Rock", "rock"),
  //     ("lofi_master.wav", "LoFi", "lofi"),
  //     ("pop_master.wav", "Pop", "pop"),
  //     ("elec_master.wav", "Electronic", "elec"),
  //   ];

  //   String output = await _getJuceDir(isDenoise: isDenoise);
  //   _masterFilters.add(MasterFilterItem(
  //     title: normalFilter,
  //     fileURL: isDenoise ? "/dummy.wav" : normalizedRecordingPath,
  //     isLoading: false,
  //     thumbnailPath: "${AssetPaths.filterIcons}/normal.png",
  //   ));

  //   for (var i = 0; i < fileNames.length; i++) {
  //     var (fileName, title, alias) = fileNames[i];
  //     var item = MasterFilterItem(
  //       title: title,
  //       fileURL: "$output$fileName",
  //       thumbnailPath: "${AssetPaths.filterIcons}/$alias.png",
  //       isLoading: true,
  //     );
  //     _masterFilters.add(item);
  //   }
  // }

  // Future<void> _startFilterProcessingQueue({required bool isDenoise}) async {
  //   try {
  //     if (!File(rawVocalPath).existsSync()) {
  //       logger.e('Recorded audio not found: $rawVocalPath');
  //       emit(VocalFilterErrorState('Recorded audio not found: $rawVocalPath'));
  //       return;
  //     }

  //     if (!isDenoise) {
  //       try {
  //         var filter = _filters.firstWhere((e) => !e.isDenoise && e.title == normalFilter);
  //         filter.fileURL = rawVocalPath;
  //         filter.isLoading = false;
  //         emit(VocalFilterItemUpdated());
  //       } catch (_) {}
  //     }

  //     String output = await _getJuceDir(isDenoise: isDenoise);

  //     final vocalConfigPath = await AssetPaths.extractAsset('assets/juce_configs/config.json');
  //     final masterConfigPath = await AssetPaths.extractAsset('assets/juce_configs/config_master.json');
  //     juce.initJuceMixPlayerAudioFilters(vocalConfigPath, masterConfigPath);

  //     if (await Directory(output).list().isEmpty) {
  //       throw Exception('Unable to create filters');
  //     }

  //     emit(VocalFilterItemUpdated());
  //   } catch (error) {
  //     logger.e("juce error: $error");
  //     emit(VocalFilterErrorState(error.toString()));
  //   }
  // }

  // Future<void> processAudioFiles() async {
  //   await Future.wait([createFilterItems(isDenoise: false), createMasterFilterItems(isDenoise: false)]); //createFilterItems(isDenoise: true),
  //   await Future.wait([_startFilterProcessingQueue(isDenoise: false)]); //_startFilterProcessingQueue(isDenoise: true)
  // }

  //   Future<void> _onProcess(FilterEditChanged event, dynamic _) async {
  //   await _mixWavFiles(event);
  // }

  // void _deletePreviousMixFile() {
  //   var mixFilePath = previousJuceComposeModel?.output;
  //   if (mixFilePath != null) {
  //     try {
  //       if (File(mixFilePath).existsSync()) {
  //         File(mixFilePath).deleteSync();
  //       }
  //     } catch (_) {}
  //   }
  // }

  // Future<void> _cleanMixOutputDirectory() async {
  //   try {
  //     String dir = await _getMixWavOutputDirectory();
  //     if (Directory(dir).existsSync()) {
  //       Directory(dir).deleteSync(recursive: true);
  //     }
  //   } catch (_) {}
  // }