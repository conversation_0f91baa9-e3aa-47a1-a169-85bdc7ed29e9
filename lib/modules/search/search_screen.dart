import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/molecules/app_search_bar.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/molecules/song_list_item.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/feed/model/feed_request_type.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/search/bloc/search_cubit.dart';
import 'package:melodyze/modules/search/model/search_result.dart';
import 'package:melodyze/modules/search/repo/search_repo.dart';
import 'package:melodyze/modules/search/service/search_service.dart';

@RoutePage()
class SearchScreen extends StatelessWidget {
  final String? gridId;
  final String? tileId;
  final String? title;
  const SearchScreen({super.key, this.gridId, this.tileId, this.title});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
        create: (context) {
          final cubit = SearchCubit(SearchRepository(SearchService()), title: title);
          if (gridId != null) {
            cubit.loadGridSongs(gridId!);
          } else if (tileId != null) {
            cubit.loadFromTileSongs(tileId!);
          }
          return cubit;
        },
        child: _SearchScreen(
          autoFocus: gridId == null && tileId == null,
        ));
  }
}

class _SearchScreen extends StatelessWidget {
  final bool autoFocus;

  const _SearchScreen({this.autoFocus = false});

  @override
  Widget build(BuildContext context) {
    final isFromSearch = context.read<SearchCubit>().title == 'Search';
    return MeloScaffold(
      secondaryAction: (context) {
        return Expanded(
          child: Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: AppSearchBar(
              autofocus: autoFocus,
              onTextChanged: (value) => context.read<SearchCubit>().searchV2(value),
            ),
          ),
        );
      },
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0),
          child: BlocBuilder<SearchCubit, BlocState>(
            builder: (context, state) {
              if (state is LoadingState) {
                return const Center(
                  child: AppCircularProgressIndicator(),
                );
              }
              if (state is EmptyDataState) {
                return Center(
                  child: const Text('No data found'),
                );
              }

              if (state is BlocSuccessState<SearchResult>) {
                final searchResult = state.data;
                final matchedSongs = searchResult.matchedSongs;
                final youMayLikeSongs = searchResult.youMayLikeSongs;

                return SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (isFromSearch == false)
                        Padding(
                          padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
                          child: Text(
                            context.read<SearchCubit>().title,
                            style: AppTextStyles.headline4.copyWith(
                              fontFamily: AppFonts.inter,
                            ),
                          ),
                        ),
                      if (matchedSongs.isNotEmpty) ...[
                        _buildSongListSection(context, matchedSongs),
                      ],
                      if (youMayLikeSongs.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.only(left: 16.0, top: 16.0, bottom: 8.0),
                          child: Text(
                            'You may also like',
                            style: AppTextStyles.headline4.copyWith(
                              fontFamily: AppFonts.inter,
                            ),
                          ),
                        ),
                        _buildSongListSection(context, youMayLikeSongs),
                      ],
                    ],
                  ),
                );
              }

              if (state is BlocFailureState) {
                return Center(child: Text(state.error.message));
              }
              return const SizedBox.shrink();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSongListSection(BuildContext context, List<SongModel> songs) {
    return ListView.separated(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemCount: songs.length,
      padding: EdgeInsets.only(bottom: 8.0),
      itemBuilder: (context, index) {
        final song = songs[index];
        return Padding(
          padding: const EdgeInsets.only(left: 16.0),
          child: SongListItem(
            title: song.title,
            subtitle: song.singer,
            s3ImagePath: song.thumbnailPath,
            onPressed: () => context.pushRoute(SongPersonalizationRoute(song: song)),
            onIconPressed: () {
              context.pushRoute(
                FeedRoute(
                  pageName: FeedPageSourceType.search.name,
                  clickedSource: FeedClickedSourceType.thumbnail.name,
                  masterSongId: song.id,
                  lang: song.lang
                ),
              );
            },
          ),
        );
      },
      separatorBuilder: (context, index) => Padding(
        padding: const EdgeInsets.symmetric(vertical: 12.0),
        child: SizedBox(
          width: MediaQuery.sizeOf(context).width,
          child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
        ),
      ),
    );
  }
}
