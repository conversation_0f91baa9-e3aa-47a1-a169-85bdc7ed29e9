import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/recorder/recorder_channel.dart';

class StartRecordingEvent extends BaseEvent {
  @override
  List<Object?> get props => [];
}

class StopRecordingEvent extends BaseEvent {
  final bool isDisposed;
  final bool isSongEnded;
  final bool discardRecording;

  const StopRecordingEvent({
    this.isDisposed = false,
    this.isSongEnded = false,
    this.discardRecording = false,
  });
  @override
  List<Object?> get props => [];
}

class InitAudioRecorderEvent extends BaseEvent {
  final bool showTips;
  const InitAudioRecorderEvent({this.showTips = true});
  @override
  List<Object?> get props => [];
}

class ChangeAudioInputEvent extends BaseEvent {
  final RecorderDevice mic;
  const ChangeAudioInputEvent({required this.mic});
  @override
  List<Object?> get props => [mic];
}