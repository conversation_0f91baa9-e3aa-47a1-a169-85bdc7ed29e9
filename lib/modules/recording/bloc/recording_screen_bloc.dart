// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:audio_session/audio_session.dart';
import 'package:flutter/widgets.dart';
import 'package:melodyze/core/core_modules/file_downloader/file_downloader.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/recorder/recorder_channel.dart';
import 'package:melodyze/core/ui/utilities/screen_utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/core/wrappers/remote_config_helper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_events.dart';
import 'package:melodyze/modules/recording/bloc/recording_screen_states.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/recording/repo/recording_repo.dart';
import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';
import 'package:record/record.dart';

part 'android/android_recording_screen_bloc.dart';
part 'android/recording_screen_android_mixin.dart';

// FIXED: 1. call `_initAudioRecorder` when reset recorder and back to rec page.
// FIXED: 2. update lyrics highlights using player progress
// FIXME: 3. handle RecorderChannelState.finishedFailed

class RecordingScreenBloc extends SafeBloc {
  final AudioPlayerBloc audioPlayerBloc;
  final RecordingRepo recordingRepo;
  final AnnotatedData annotatedData;
  final DownloadWavFileCubit downloadWavFileCubit;
  int timeDiff = 0;

  List<RecorderDevice> micList = [];
  RecorderDevice? selectedMic;

  bool stopRecordingHandled = false;
  bool isFrontCameraEnabled = true;
  RecorderChannel? record;
  String? vocalPath;
  StreamSubscription<AudioPlayerState>? _audioPlayerListener;
  late FileDownloader _fileDownloader;
  AppLifecycleListener? _listener;
  bool hasClipped = false;
  ({double min, double max}) audioLevelRange = (min: -160.0, max: 0.0);

  // Android specific variables
  AudioRecorder? androidRecord;
  List<InputDevice> androidMicList = [];
  InputDevice get androidSelectedMic => androidMicList.firstWhere((device) => device.id == selectedMic?.uid);
  StreamSubscription<RecordState>? _recordListener;
  DateTime? bgmStartTime;
  DateTime? recordingStartTime;

  RecordingScreenBloc({
    required this.audioPlayerBloc,
    required this.recordingRepo,
    required this.annotatedData,
    required this.downloadWavFileCubit,
  }) : super(InitialState()) {
    record = RecorderChannel();
    _fileDownloader = FileDownloader();
    //Await can be added for this wakelock.
    ScreenUtils.keepScreenOn();

    on<InitAudioRecorderEvent>(_initAudioRecorder);
    on<StartRecordingEvent>(_startRecording);
    on<StopRecordingEvent>(_stopRecording);
    on<ChangeAudioInputEvent>(_changeAudioInput);
    add(InitAudioRecorderEvent());
  }

  static RecordingScreenBloc platformSpecific({
    required AudioPlayerBloc audioPlayerBloc,
    required RecordingRepo recordingRepo,
    required AnnotatedData annotatedData,
    required DownloadWavFileCubit downloadWavFileCubit,
  }) {
    return Platform.isAndroid
        ? AndroidRecordingScreenBloc(
            audioPlayerBloc: audioPlayerBloc,
            recordingRepo: recordingRepo,
            annotatedData: annotatedData,
            downloadWavFileCubit: downloadWavFileCubit,
          )
        : RecordingScreenBloc(
            audioPlayerBloc: audioPlayerBloc,
            recordingRepo: recordingRepo,
            annotatedData: annotatedData,
            downloadWavFileCubit: downloadWavFileCubit,
          );
  }

  @override
  Future<void> close() async {
    logger.d("RecordingScreenBloc closed");
    await ScreenUtils.keepScreenOff();
    if (Platform.isIOS) await record?.dispose();
    await androidRecord?.dispose();
    await _audioPlayerListener?.cancel();
    _audioPlayerListener = null;
    _setAudioCategory();
    _listener?.dispose();
    unawaited(super.close());
  }

  Future<void> _startRecording(StartRecordingEvent event, _) async {
    await _startAudioRecording(event);
  }

  Future<void> _stopRecording(StopRecordingEvent event, _) async {
    await _stopAudioRecording(event);
  }

  void updateAudioLevelRange() {
    final configJson = RemoteConfigHelper.getString(RemoteConfigKeys.audioLevelRange);
    final rangeMap = jsonDecode(configJson) as Map<String, dynamic>;
    audioLevelRange = (
      min: double.parse(rangeMap['min'].toString()),
      max: double.parse(rangeMap['max'].toString()),
    );
  }

  /// Points of failure
  /// 1. No audio input devices found
  /// 2. Microphone permission not granted
  /// 3. Unknown failure to initialize audio recorder - should be logged to crashlytics for better debugging
  Future<void> _initAudioRecorder(InitAudioRecorderEvent event, _) async {
    updateAudioLevelRange();
    _listener ??= AppLifecycleListener(
      onHide: () {
        add(StopRecordingEvent(discardRecording: true));
      },
      onShow: () {
        add(InitAudioRecorderEvent(showTips: false));
      },
    );
    emit(AudioRecorderInitializingState());
    try {
      if ((await record?.hasPermission() ?? false)) {
        if (event.showTips) emit(ShowRecordingMessage());
      }
      hasClipped = false;

      vocalPath ??= await PathWrapper.getVocalRecordingPath();
      await _fileDownloader.download(resource: annotatedData.songPath, resourceType: FileResourceType.s3Url, type: FileType.bgm);
      String bgmPath = _fileDownloader.downloadedFile!.path;
      await record?.prepare(recPath: vocalPath!, bgmPath: bgmPath);

      var list = await record?.listInputDevices();
      micList = list ?? [];

      if (micList.isEmpty) {
        emit(const RecordingErrorState(errorMessage: 'No audio input devices found'));
        return;
      }

      logger.d('Available audio input devices: ${micList.map((device) => device.uid).toList()}');

      selectedMic = micList.firstWhere(
        (device) => device.portName.toLowerCase().contains('usb'),
        // If no USB mic found, fallback to built-in/default device mic
        orElse: () => micList.firstWhere(
          (device) => device.portName.toLowerCase().contains('built-in') || device.portName.toLowerCase().contains('default'),
          // If no built-in/default mic found, use the first available mic
          orElse: () => micList[0],
        ),
      );
      await record?.selectDevice(selectedMic?.uid ?? "");

      logger.i('Selected microphone: ${selectedMic?.portName}');

      record?.setRecorderStateListener((state) {
        logger.i("RecorderChannelState $state");
        if (state == RecorderChannelState.recording) {
          emit(RecordingInProgressState());
        } else if (state == RecorderChannelState.finishedSuccess) {
          add(const StopRecordingEvent(isSongEnded: true));
        } else if (state == RecorderChannelState.finishedFailed) {
          // FIXME: handle this
        }
      });

      record?.setPlayerProgressListener((time) {
        emit(RecordingUpdatePlayerProgressState(progressInMilliseconds: time));
      });

      record?.setAudioLevelUpdateListener((level) {
        if (level >= -4.0) {
          hasClipped = true;
        }
        emit(RecordingUpdateAudioLevelState(audioLevel: level));
      });

      emit(AudioRecorderReadyState());
    } catch (e, stackTrace) {
      logger.e('Failed to initialize audio recorder', error: e, recordError: true, stackTrace: stackTrace);
      if (Platform.isIOS && e.toString().toLowerCase().contains('microphone permission denied')) {
        emit(ShowMicPermissionDialogState());
      } else {
        emit(RecordingErrorState(errorMessage: 'Failed to initialize audio recorder: ${e.toString()}'));
      }
    }
  }

  Future<void> _startAudioRecording(StartRecordingEvent event) async {
    stopRecordingHandled = false;
    try {
      emit(RecordLoadingState());

      if (!(await record?.hasPermission() ?? false)) {
        emit(ShowMicPermissionDialogState());
        // emit(const RecordingErrorState(errorMessage: 'No audio recording permission'));
        return;
      }

      await checkSelectedMicAvailability(selectedMic: selectedMic);
      timeDiff = await record?.start() ?? 0;

      unawaited(downloadWavFileCubit.downloadWavFile(
        masterSongId: annotatedData.masterSongId,
        genre: annotatedData.genre,
        genreId: annotatedData.genreId,
        scale: annotatedData.scale,
        tempo: annotatedData.tempo,
      ));
    } catch (e) {
      emit(RecordingErrorState(errorMessage: 'Error starting recording: ${e.toString()}'));
    }
  }

  Future<void> _stopAudioRecording(StopRecordingEvent event) async {
    if (stopRecordingHandled) {
      return;
    }
    stopRecordingHandled = true;
    try {
      emit(RecordLoadingState());
      await record?.stop();

      if (event.isDisposed) {
        return;
      }
      if (event.discardRecording) {
        emit(AudioRecorderReadyState());
        return;
      }

      // Apply stereo normalization to the recorded vocal
      final normalizedRecordingPath = await PathWrapper.getNormalizedVocalRecordingPath();
      await JuceKitWrapper().applyStereoAndNormalizeVocal(vocalPath!, normalizedRecordingPath);

      _setAudioCategory();
      emit(
        RecordingSuccessState(
          outputFilePath: vocalPath!,
          normalizedOutputFilePath: normalizedRecordingPath,
          isAudioOnly: true,
          timeDiff: timeDiff,
          hasClipped: hasClipped,
        ),
      );
    } catch (e) {
      emit(RecordingErrorState(errorMessage: 'Error stopping recording: $e'));
    }
  }

  FutureOr<void> _changeAudioInput(ChangeAudioInputEvent event, _) async {
    try {
      emit(AudioRecorderInitializingState());
      await checkSelectedMicAvailability(selectedMic: event.mic);
      selectedMic = event.mic;
      await record?.selectDevice(event.mic.uid);
      emit(AudioRecorderReadyState());
    } catch (e) {
      emit(RecordingErrorState(errorMessage: 'Failed to change audio input: ${e.toString()}'));
    }
  }

  void _setAudioCategory() {
    AudioSession.instance.then((session) {
      session.configure(const AudioSessionConfiguration.music());
    });
  }

  Future<void> checkSelectedMicAvailability({RecorderDevice? selectedMic}) async {
    final devices = await record?.listInputDevices() ?? [];
    if (!devices.any((device) => device.uid == selectedMic?.uid)) {
      emit(const RecordingErrorState(errorMessage: 'Selected audio device is no longer available'));
      return;
    }
  }
}
