import 'package:melodyze/core/generic_bloc/states.dart';

class RecordingErrorState extends BlocState {
  final String errorMessage;

  const RecordingErrorState({required this.errorMessage});

  @override
  List<Object> get props => [errorMessage];

  @override
  bool? get stringify => true;
}

class RecordingSuccessState extends BlocState {
  final String outputFilePath;
  final String normalizedOutputFilePath;
  final bool isAudioOnly;
  final int timeDiff;
  final bool hasClipped;

  const RecordingSuccessState({
    required this.outputFilePath,
    required this.normalizedOutputFilePath,
    this.isAudioOnly = false,
    required this.timeDiff,
    required this.hasClipped,
  });

  @override
  List<Object> get props => [outputFilePath, normalizedOutputFilePath, isAudioOnly, timeDiff, hasClipped];
}

class RecordLoadingState extends BlocState {
  @override
  List<Object?> get props => [];
}

class RecordingInProgressState extends BlocState {
  @override
  List<Object?> get props => [];
}

class AudioRecorderInitializingState extends BlocState {
  @override
  List<Object?> get props => [];
}

class AudioRecorderReadyState extends BlocState {
  @override
  List<Object?> get props => [];
}

class ShowRecordingMessage extends BlocState {
  @override
  List<Object?> get props => [];
}

class RecordingUpdatePlayerProgressState extends BlocState {
  final int progressInMilliseconds;

  const RecordingUpdatePlayerProgressState({required this.progressInMilliseconds});

  @override
  List<Object> get props => [progressInMilliseconds];
}

class RecordingUpdateAudioLevelState extends BlocState {
  final double audioLevel;

  const RecordingUpdateAudioLevelState({required this.audioLevel});

  @override
  List<Object> get props => [audioLevel];
}

class ShowMicPermissionDialogState extends BlocState {
  @override
  List<Object?> get props => [];
}
