part of '../recording_screen_bloc.dart';

mixin RecordingScreenAndroidMixin on RecordingScreenBloc {
  RecorderDevice fromAndroidDevice(InputDevice device) {
    return RecorderDevice(
      uid: device.id,
      portName: device.label,
      portType: device.id,
    );
  }

  Future<void> initAndroidAudioRecorder(InitAudioRecorderEvent event, _) async {
    updateAudioLevelRange();
    _listener ??= AppLifecycleListener(
      onHide: () {
        add(StopRecordingEvent(discardRecording: true));
      },
      onShow: () {
        add(InitAudioRecorderEvent(showTips: false));
      },
    );
    emit(AudioRecorderInitializingState());
    try {
      hasClipped = false;

      androidRecord ??= AudioRecorder();

      vocalPath ??= await PathWrapper.getVocalRecordingPath();

      androidMicList = await androidRecord?.listInputDevices() ?? [];

      micList = androidMicList.map(fromAndroidDevice).toList();

      if (androidMicList.isEmpty) {
        emit(const RecordingErrorState(errorMessage: 'No audio input devices found'));
        return;
      }

      logger.d('Available audio input devices: ${androidMicList.map((device) => device.label).toList()}');

      final androidSelectedMic = androidMicList.firstWhere(
        (device) => device.label.toLowerCase().contains('usb'),
        // If no USB mic found, fallback to built-in/default device mic
        orElse: () => androidMicList.firstWhere(
          (device) => device.label.toLowerCase().contains('built-in') || device.label.toLowerCase().contains('default'),
          // If no built-in/default mic found, use the first available mic
          orElse: () => androidMicList[0],
        ),
      );

      selectedMic = micList.firstWhere((mic) => mic.uid == androidSelectedMic.id);

      logger.i('Selected microphone: ${androidSelectedMic.label}');

      if (!(await androidRecord?.hasPermission() ?? false)) {
        // emit(const RecordingErrorState(errorMessage: 'Microphone permission not granted'));
        emit(ShowMicPermissionDialogState());
        return;
      }

      _audioPlayerListener ??= audioPlayerBloc.stream.listen((state) {
        if (state is AudioPlayerStopped) {
          add(const StopRecordingEvent(isSongEnded: true));
        }
      });

      // Update to track recording start time
      _recordListener ??= androidRecord?.onStateChanged().listen((state) {
        if (state == RecordState.record) {
          audioPlayerBloc.add(AudioPlayerPlayEvent());
          emit(RecordingInProgressState());
        }
      });

      if ((await androidRecord?.hasPermission() ?? false)) {
        if (event.showTips) emit(ShowRecordingMessage());
      }

      emit(AudioRecorderReadyState());
    } catch (e, stackTrace) {
      logger.e('Failed to initialize audio recorder', error: e, recordError: true, stackTrace: stackTrace);
      emit(RecordingErrorState(errorMessage: 'Failed to initialize audio recorder: ${e.toString()}'));
    }
  }

  Future<void> startAndroidAudioRecording(StartRecordingEvent event, _) async {
    stopRecordingHandled = false;
    try {
      emit(RecordLoadingState());

      if (!(await androidRecord?.hasPermission() ?? false)) {
        emit(ShowMicPermissionDialogState());
        // emit(const RecordingErrorState(errorMessage: 'No audio recording permission'));
        return;
      }

      await checkAndroidSelectedMicAvailability(selectedMic: androidSelectedMic);

      recordingStartTime = DateTime.now();
      logger.d('Recording started at: $recordingStartTime');

      await androidRecord?.start(
        RecordConfig(
          encoder: AudioEncoder.wav,
          device: androidSelectedMic,
          sampleRate: 48000,
        ),
        path: vocalPath!,
      );

      unawaited(downloadWavFileCubit.downloadWavFile(
        masterSongId: annotatedData.masterSongId,
        genre: annotatedData.genre,
        genreId: annotatedData.genreId,
        scale: annotatedData.scale,
        tempo: annotatedData.tempo,
      ));
    } catch (e) {
      emit(RecordingErrorState(errorMessage: 'Error starting recording: ${e.toString()}'));
    }
  }

  Future<void> stopAndroidAudioRecording(StopRecordingEvent event, _) async {
    if (stopRecordingHandled) {
      return;
    }
    stopRecordingHandled = true;
    try {
      if (!((await androidRecord?.isRecording() ?? false))) {
        return;
      }
      emit(RecordLoadingState());
      await androidRecord?.stop();

      if (event.isDisposed) {
        return;
      }
      if (event.discardRecording) {
        emit(AudioRecorderReadyState());
        return;
      }

      // Apply stereo normalization to the recorded vocal
      final normalizedRecordingPath = await PathWrapper.getNormalizedVocalRecordingPath();
      await JuceKitWrapper().applyStereoAndNormalizeVocal(vocalPath!, normalizedRecordingPath);

      _setAudioCategory();
      emit(
        RecordingSuccessState(
          outputFilePath: vocalPath!,
          normalizedOutputFilePath: normalizedRecordingPath,
          isAudioOnly: true,
          timeDiff: timeDiff,
          hasClipped: hasClipped,
        ),
      );
      if (!event.isSongEnded) {
        audioPlayerBloc.add(AudioPlayerStopEvent());
      }
    } catch (e) {
      emit(RecordingErrorState(errorMessage: 'Error stopping recording: $e'));
    }
  }

  Future<void> changeAndrioidAudioInput(ChangeAudioInputEvent event, _) async {
    try {
      emit(AudioRecorderInitializingState());
      await checkAndroidSelectedMicAvailability(selectedMic: androidMicList.firstWhere((device) => device.id == event.mic.uid));
      selectedMic = event.mic;
      emit(AudioRecorderReadyState());
    } catch (e) {
      logger.e('Failed to change audio input', error: e, recordError: true);
      emit(RecordingErrorState(errorMessage: 'Failed to change audio input: ${e.toString()}'));
    }
  }

  Future<void> checkAndroidSelectedMicAvailability({InputDevice? selectedMic}) async {
    final devices = await androidRecord?.listInputDevices() ?? [];
    if (!devices.any((device) => device.id == selectedMic?.id)) {
      emit(const RecordingErrorState(errorMessage: 'Selected audio device is no longer available'));
      return;
    }
  }
}
