// ignore_for_file: no_wildcard_variable_uses

part of '../recording_screen_bloc.dart';

class AndroidRecordingScreenBloc extends RecordingScreenBloc with RecordingScreenAndroidMixin {
  AndroidRecordingScreenBloc({
    required super.audioPlayerBloc,
    required super.recordingRepo,
    required super.annotatedData,
    required super.downloadWavFileCubit,
  }) {
    _fileDownloader = FileDownloader();
    //Await can be added for this wakelock.
    ScreenUtils.keepScreenOn();
    audioPlayerBloc.stream.listen((state) {
      if (state is AudioPlayerPlaying) {
        bgmStartTime = state.startTime;

        if (recordingStartTime != null) {
          timeDiff = bgmStartTime!.difference(recordingStartTime!).inMilliseconds;
          logger.d('BGM to Recording delay: $timeDiff ms');
        }
      }
      if (state is AudioPlayerStopped) {
        bgmStartTime = null;
        recordingStartTime = null;
      }
    });
  }

  @override
  Future<void> _initAudioRecorder(InitAudioRecorderEvent event, _) {
    return initAndroidAudioRecorder(event, _);
  }

  @override
  Future<void> _startRecording(StartRecordingEvent event, _) {
    return startAndroidAudioRecording(event, _);
  }

  @override
  Future<void> _stopRecording(StopRecordingEvent event, _) {
    return stopAndroidAudioRecording(event, _);
  }

  @override
  Future<void> _changeAudioInput(ChangeAudioInputEvent event, _) {
    return changeAndrioidAudioInput(event, _);
  }

  @override
  Future<void> close() async {
    await Future.wait([
      if (_audioPlayerListener != null) _audioPlayerListener!.cancel(),
      if (_recordListener != null) _recordListener!.cancel(),
      if (androidRecord != null) androidRecord!.cancel(),
    ]);
    unawaited(super.close());
  }
}
