import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';
import 'package:melodyze/modules/profile/bloc/profile_bloc.dart';
import 'package:melodyze/modules/profile/bloc/profile_event.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/ui/widget/profile_recording_item.dart';

class ProfileFinalRecordings extends StatefulWidget {
  final List<RecordingModel> recordings;
  const ProfileFinalRecordings({super.key, required this.recordings});

  @override
  State<ProfileFinalRecordings> createState() => _ProfileFinalRecordingsState();
}

class _ProfileFinalRecordingsState extends State<ProfileFinalRecordings> {
  @override
  Widget build(BuildContext context) {
    return widget.recordings.isEmpty
        ? const Center(child: Text('No recordings found'))
        : RefreshIndicator(
            onRefresh: () async {
              context.read<ProfileBloc>().add(const ProfileLoadDataEvent());
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: ListView.separated(
                itemCount: widget.recordings.length,
                padding: EdgeInsets.only(bottom: 16.0, top: 24.0),
                itemBuilder: (context, index) {
                  final recording = widget.recordings[index];
                  return Padding(
                    padding: EdgeInsets.only(
                      left: 16.0,
                      bottom: index == widget.recordings.length - 1 ? 120.0 : 0.0,
                    ),
                    child: RecordingListItem(
                      type: RecordingListType.finalRecording,
                      recording: recording,
                      onAction: (value) {
                        setState(() {
                          widget.recordings.remove(value);
                        });
                      },
                    ),
                  );
                },
                separatorBuilder: (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12.0),
                  child: ImageLoader.fromAsset(AssetPaths.gradientdivider),
                ),
              ),
            ),
          );
  }
}
