import 'package:freezed_annotation/freezed_annotation.dart';

part 'recording_model.freezed.dart';
part 'recording_model.g.dart';

@freezed
class RecordingModel with _$RecordingModel {
  factory RecordingModel({
    @Json<PERSON>ey(name: '_id') required String id,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'title') @Default('') String title,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'singer') @Default('') String singer,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'final_video_file_path') @Default('') String finalVideoFilePath,
    @<PERSON>son<PERSON>ey(name: 'final_mixed_audio_path') @Default('') String finalMixedAudioPath,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'vocal_filter_name') @Default('') String vocalFilterName,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'genre') @Default('') String genre,
    @Json<PERSON>ey(name: 'master_song_id') @Default('') String masterSongId,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'thumbnail_path') @Default('') String thumbnailPath,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'tempo') @Default('') String tempo,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'scale') @Default('') String scale,
    @<PERSON><PERSON><PERSON><PERSON>(name: 'is_final_save') @Default(false) bool isFinalSave,
    @Json<PERSON>ey(name: 'is_deleted') @Default(false) bool isDeleted,
    @JsonKey(name: 'media_type') @Default('audio') String mediaType,
    @Json<PERSON>ey(name: 'created_at') @Default(0) int createdAt,
    @JsonKey(name: 'feed_type') @Default('') String feedType,
  }) = _RecordingModel;

  factory RecordingModel.fromJson(Map<String, dynamic> payload) => _$RecordingModelFromJson(payload);
}
