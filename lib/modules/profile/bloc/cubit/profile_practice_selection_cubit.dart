import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';

part 'profile_practice_selection_state.dart';

class ProfilePracticeSelectionCubit extends Cubit<ProfilePracticeSelectionState> {
  ProfilePracticeSelectionCubit() : super(ProfilePracticeSelectionInitial());

  RecordingModel? selectedRecording;

  bool get isRecordingSelected => selectedRecording != null;

  void onPracticeRecordingSelected(RecordingModel recording) {
    selectedRecording = recording;
    emit(ProfilePracticeSelectionSelected(recording));
  }

  void onPracticeRecordingUnselected() {
    selectedRecording = null;
    emit(ProfilePracticeSelectionInitial());
  }
}
