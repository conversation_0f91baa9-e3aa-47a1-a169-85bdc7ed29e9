part of 'profile_practice_selection_cubit.dart';

// @freezed
// class ProfilePracticeSelectionState with _$ProfilePracticeSelectionState {
//   final RecordingModel? recording;

//   const factory ProfilePracticeSelectionState.initial() = _Initial;

//   const factory ProfilePracticeSelectionState.selected() = _Selected;
// }

sealed class ProfilePracticeSelectionState extends BlocState {
  final RecordingModel? recording;
  const ProfilePracticeSelectionState(
    this.recording,
  );

  @override
  List<Object> get props => [];
}

final class ProfilePracticeSelectionInitial extends ProfilePracticeSelectionState {
  const ProfilePracticeSelectionInitial() : super(null);
}

final class ProfilePracticeSelectionSelected extends ProfilePracticeSelectionState {
  const ProfilePracticeSelectionSelected(RecordingModel super.recording);
}
