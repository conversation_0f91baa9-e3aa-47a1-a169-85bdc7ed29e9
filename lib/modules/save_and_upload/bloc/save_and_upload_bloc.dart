import 'dart:async';
import 'dart:io';

import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/services/aws_client.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/device_info_wrapper.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/vocal_filters/bloc/vocal_filters_bloc.dart';

part 'save_and_upload_event.dart';
part 'save_and_upload_state.dart';

class SaveAndUploadBloc extends SafeBloc<SaveAndUploadEvent, SaveAndUploadState> {
  final String? userId;
  final ShareDataBloc shareDataBloc;
  final VocalfiltersBloc vocalfiltersBloc;
  final JuceMixBloc juceMixBloc;
  final juceKitWrapper = JuceKitWrapper();

  SaveAndUploadBloc({
    required this.shareDataBloc,
    required this.vocalfiltersBloc,
    required this.juceMixBloc,
    this.userId,
  }) : super(SaveAndUploadInitial()) {
    on<UploadEvent>(_upload);
    add(UploadEvent());
  }

  final DI _di = DI();

  @override
  Future<void> close() async {
    unawaited(super.close());
  }

  FutureOr<void> _upload(UploadEvent event, _) async {
    emit(UploadProcessingState());
    try {
      final savedFileName = FileUtils.toSnakeCase(shareDataBloc.song!.title);
      String? finalRecordS3path = "";

      final finalRecordOutputPath = await PathWrapper.getFinalRecordOutputPath();
      await juceMixBloc.exportComposedAudio(finalRecordOutputPath);
      if ((File(finalRecordOutputPath).existsSync())) {
        final finalRecordFlacPath = "${await PathWrapper.getTempDownloadPath()}/$savedFileName.flac";
        await juceKitWrapper.convertWavToFlac(finalRecordOutputPath, finalRecordFlacPath);
        finalRecordS3path = await DI().resolve<AwsClient>().uploadFileByS3SignedUrl(finalRecordFlacPath, Endpoints.getUploadFinalMixedAudioSignedUrl);
      }

      final userDevice = await DeviceInfoWrapper.getSystemInfo();

      final recordingBody = {
        "title": savedFileName,
        "master_song_id": shareDataBloc.song!.id,
        "genre": shareDataBloc.annotatedData!.genre,
        "genre_id": shareDataBloc.annotatedData!.genreId,
        "scale": shareDataBloc.annotatedData!.scale,
        "tempo": shareDataBloc.annotatedData!.tempo,
        "input_mic": vocalfiltersBloc.inputMic,
        "vocal_volume": vocalfiltersBloc.vocalVolume,
        "bgm_volume": vocalfiltersBloc.musicVolume,
        "vocal_filter_name": vocalfiltersBloc.vocalFilterName,
        "master_filter_name": vocalfiltersBloc.masterFilterName,
        "device": userDevice.device,
        "os": userDevice.os,
        "raw_audio_file_path": vocalfiltersBloc.recordedVocalS3path,
        "final_mixed_audio_path": finalRecordS3path,
        "latency": vocalfiltersBloc.userGivenDelay,
        "denoise": false,
      };

      final response = await _di.resolve<ApiClient>().post(Endpoints.saveRecording, body: recordingBody);
      if (response?['success'] == true) {
        emit(UploadSuccessState());
      } else {
        emit(UploadFailureState(response?['error'] ?? ''));
      }
    } catch (error) {
      logger.e("Error saving recording: ${error.toString()}");
      emit(UploadFailureState(error.toString()));
    } finally {
      await juceMixBloc.close();
    }
  }
}
