// class MasterFilterItem {
//   final String title;
//   final String configFilePath;
//   final String thumbnailPath;
//   final int id;
//   String? fileURL;
//   String? error;
//   bool isLoading;

//   static int _lastId = 1;

//   MasterFilterItem({
//     required this.title,
//     required this.thumbnailPath,
//     this.configFilePath = "",
//     this.fileURL,
//     this.isLoading = true,
//   }) : id = _lastId {
//     _lastId++;
//   }
// }
