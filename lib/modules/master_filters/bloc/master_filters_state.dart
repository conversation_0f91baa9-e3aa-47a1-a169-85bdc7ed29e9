// part of 'master_filters_bloc.dart';

// abstract class MasterFiltersState extends BlocState {
//   const MasterFiltersState();
// }

// class MasterFilterInitialState extends MasterFiltersState {
//   @override
//   List<Object?> get props => [];
// }

// class MasterFilterLoadingState extends MasterFiltersState {
//   @override
//   List<Object?> get props => [];
// }

// class MasterFilterLoadedState extends MasterFiltersState {
//   final bool isLoading;

//   const MasterFilterLoadedState({this.isLoading = false});

//   @override
//   List<Object?> get props => [isLoading];
// }

// class MasterFilterErrorState extends MasterFiltersState {
//   final String error;
//   const MasterFilterErrorState(this.error);

//   @override
//   List<Object?> get props => [];
// }

// class MasterFilterPlayerErrorState extends MasterFiltersState {
//   final String error;
//   const MasterFilterPlayerErrorState(this.error);

//   @override
//   List<Object?> get props => [];
// }

// class MasterFilterPlayingState extends MasterFiltersState {
//   @override
//   List<Object?> get props => [];
// }

// class MasterFilterPausedState extends MasterFiltersState {
//   @override
//   List<Object?> get props => [];
// }

// class MasterFilterCurrentPositionState extends MasterFiltersState {
//   final Duration position;
//   const MasterFilterCurrentPositionState(this.position);
//   @override
//   List<Object?> get props => [position];
// }

// class MasterFilterItemUpdated extends MasterFiltersState {
//   final int value;

//   MasterFilterItemUpdated() : value = DateTime.now().millisecondsSinceEpoch;

//   @override
//   List<Object?> get props => [value];
// }
