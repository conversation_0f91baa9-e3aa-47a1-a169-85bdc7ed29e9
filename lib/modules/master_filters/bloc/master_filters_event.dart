// // ignore_for_file: public_member_api_docs, sort_constructors_first
// part of 'master_filters_bloc.dart';

// abstract class MasterFiltersEvent extends BaseEvent {
//   const MasterFiltersEvent();
// }

// class MasterFiltersLoadEvent extends MasterFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }

// class MasterPagePlayEvent extends MasterFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }

// class MasterPagePauseEvent extends MasterFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }

// class MasterPageStopEvent extends MasterFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }

// class MasterPageSeekEvent extends MasterFiltersEvent {
//   final double milliseconds;
//   const MasterPageSeekEvent({required this.milliseconds});

//   @override
//   List<Object> get props => [milliseconds];
// }

// // class MasterPageHideControls extends MasterFiltersEvent {
// //   @override
// //   List<Object?> get props => [];
// // }

// class MasterPageShowControls extends MasterFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }

// class MasterPageSelectFilter extends MasterFiltersEvent {
//   final int selectedFilterIndex;
//   const MasterPageSelectFilter({required this.selectedFilterIndex});
//   @override
//   List<Object?> get props => [selectedFilterIndex];
// }


// class CheckWavFileDownloadStatus extends MasterFiltersEvent {
//   @override
//   List<Object?> get props => [];
// }


// class MasterFilterEditChanged extends MasterFiltersEvent {
//   final uniqueKey = DateTime.now().millisecondsSinceEpoch;
//   MasterFilterEditChanged();

//   @override
//   List<Object?> get props => [uniqueKey];
// }
