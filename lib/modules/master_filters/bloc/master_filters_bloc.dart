// import 'dart:async';
// import 'dart:io';

// import 'package:juce_mix_player/juce_mix_player.dart';
// import 'package:melodyze/core/generic_bloc/events.dart';
// import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
// import 'package:melodyze/core/generic_bloc/states.dart';
// import 'package:melodyze/core/ui/tokens/asset_paths.dart';
// import 'package:melodyze/core/utilities/utils/utils.dart';
// import 'package:melodyze/core/wrappers/app_logger.dart';
// import 'package:melodyze/core/wrappers/path_wrapper.dart';
// import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';
// import 'package:melodyze/modules/master_filters/master_filter_item.dart';
// import 'package:melodyze/modules/share/share_data_bloc.dart';
// import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
// import 'package:melodyze/modules/master_filters/bloc/master_filters_audio_player_bloc.dart';
// import 'package:melodyze/modules/vocal_filters/bloc/vocal_filters_bloc.dart';
// import 'package:rxdart/rxdart.dart';

// part 'master_filters_event.dart';
// part 'master_filters_state.dart';

// const String normalFilter = "Normal";

// class MasterFiltersBloc extends SafeBloc<MasterFiltersEvent, MasterFiltersState> {
//   final String finalVocalFilePath;
//   final String bgmWavPath;
//   double finalBgmVolume;
//   double finalVocalVolume;

//   final MasterFiltersAudioPlayerBloc masterFiltersAudioPlayerBloc;
//   final ShareDataBloc shareDataBloc;
//   final VocalfiltersBloc vocalfiltersBloc;

//   final juce = JuceKitWrapper();
//   final Debouncer _debouncer = Debouncer();
//   final List<MasterFilterItem> _filters = [];

//   String juceMasterMixOutput = "";
//   String masterFilterName = "Normal";
//   int selectedFilterIndex = 0;

//   StreamSubscription<AudioPlayerState>? _masterAudioPlayerListener;
//   Timer? _hideTimer;
//   MasterFilterEditChanged? currentEvent;
//   String? currentMixofflinePath;

//   // static const _hideControlsDuration = Duration(seconds: 4);

//   @Deprecated('Use MixerComposeModel instead.')
//   JuceOfflineMixModel? previousMixModel;

//   MixerComposeModel? previousJuceComposeModel;

//   MasterFiltersBloc({
//     required this.finalBgmVolume,
//     required this.finalVocalVolume,
//     required this.finalVocalFilePath,
//     required this.bgmWavPath,
//     required this.masterFiltersAudioPlayerBloc,
//     required this.shareDataBloc,
//     required this.vocalfiltersBloc,
//   }) : super(MasterFilterInitialState()) {
//     on<MasterFiltersLoadEvent>(_loadMasterFiltersData);
//     on<MasterPagePlayEvent>(_playAudio);
//     on<MasterPagePauseEvent>(_pauseAudio);
//     on<MasterPageStopEvent>(_stopAudio);
//     on<MasterPageSeekEvent>(_seekAudio);
//     on<MasterPageSelectFilter>(_selectFilter);
//     on<MasterFilterEditChanged>(_onFilterEditChanged,
//         transformer: (events, mapper) => events.debounceTime(const Duration(milliseconds: 500)).asyncExpand(mapper));
//     add(MasterFiltersLoadEvent());
//     _cleanMixOutputDirectory();
//     // _setFliterProgressListener();
//   }

//   @override
//   Future<void> close() async {
//     logger.d("MasterFiltersBloc closed");
//     _hideTimer?.cancel();
//     _debouncer.dispose();
//     await _masterAudioPlayerListener?.cancel();
//     unawaited(super.close());
//   }

//   // void _setFliterProgressListener() {
//   //   juce.getEventBus().on<ProcessingGraphMasterFileCreatedEvent>().listen((event) {
//   //     if (event.callerId == _filterCallerId) {
//   //       logger.i("ProcessingGraphMasterFileCreatedEvent: ${event.file}");
//   //       try {
//   //         var filter = _filters.firstWhere((e) => e.fileURL == event.file);
//   //         filter.isLoading = false;
//   //         emit(MasterFilterItemUpdated());
//   //       } catch (_) {}
//   //     }
//   //   });
//   // }

//   FutureOr<void> _loadMasterFiltersData(MasterFiltersLoadEvent event, _) async {
//     emit(MasterFilterLoadingState());
//     try {
//       final outDirPath = await _getMixWavOutputDirectory();
//       if (!Directory(outDirPath).existsSync()) {
//         await Directory(outDirPath).create();
//       }

//       juceMasterMixOutput = "$outDirPath/${DateTime.now().millisecondsSinceEpoch}.wav";
//       await createFilterItems();
//       await _mixFinalWavFiles();

//       // emit(const MasterFilterLoadedState());
//       // _masterAudioPlayerListener = masterFiltersAudioPlayerBloc.stream.listen((state) {
//       //   if (state is AudioPlayerStopped) {
//       //     add(StopEvent());
//       //   }
//       // });
//       emit(const MasterFilterLoadedState(isLoading: false));

//       if (File(juceMasterMixOutput).existsSync()) {
//         masterFiltersAudioPlayerBloc.add(AudioPlayerLoadEvent(
//           annotationId: shareDataBloc.annotatedData!.id,
//           songId: shareDataBloc.song!.id,
//           path: juceMasterMixOutput,
//           type: AudioType.file,
//           autoPlay: true,
//         ));
//       } else {
//         logger.d("_mix Wav Files failed $juceMasterMixOutput");
//         emit(const MasterFilterPlayerErrorState("File not found!"));
//       }
//     } catch (e) {
//       logger.e(e.toString());
//       emit(MasterFilterErrorState(e.toString()));
//     }
//   }

//   Future<void> changeVocalVolume(double value) async {
//     finalVocalVolume = value;
//   }

//   Future<void> changeBgmVolume(double value) async {
//     finalBgmVolume = value;
//   }

//   // void _hideControlsAfterDelay() {
//   //   _hideTimer?.cancel();
//   //   _hideTimer = Timer(_hideControlsDuration, () {
//   //     add(MasterPageHideControls());
//   //   });
//   // }

//   FutureOr<void> _playAudio(MasterPagePlayEvent event, _) async {
//     masterFiltersAudioPlayerBloc.add(AudioPlayerPlayEvent());
//     // _hideControlsAfterDelay();
//   }

//   FutureOr<void> _seekAudio(MasterPageSeekEvent event, _) async {
//     masterFiltersAudioPlayerBloc.add(AudioPlayerSeekEvent(milliseconds: event.milliseconds));
//   }

//   FutureOr<void> _pauseAudio(MasterPagePauseEvent event, _) async {
//     masterFiltersAudioPlayerBloc.add(AudioPlayerPauseEvent());
//   }

//   FutureOr<void> _stopAudio(MasterPageStopEvent event, _) async {
//     masterFiltersAudioPlayerBloc.add(AudioPlayerStopEvent());
//   }

//   FutureOr<void> _onFilterEditChanged(MasterFilterEditChanged event, dynamic _) async {
//     try {
//       final mixError = await _mixFinalWavFiles();
//       if (mixError.isNotEmpty) {
//         emit(MasterFilterErrorState(mixError));
//         return;
//       }
//       if (File(juceMasterMixOutput).existsSync()) {
//         masterFiltersAudioPlayerBloc.add(AudioPlayerLoadEvent(
//           annotationId: shareDataBloc.annotatedData!.id,
//           songId: shareDataBloc.song!.id,
//           path: selectedFilterIndex == 0 ? juceMasterMixOutput : _getFilters()[selectedFilterIndex].fileURL!,
//           type: AudioType.file,
//           autoPlay: true,
//         ));
//         emit(const MasterFilterLoadedState(isLoading: false));
//       } else {
//         logger.d("_mix Wav Files failed $juceMasterMixOutput");
//         emit(const MasterFilterPlayerErrorState("File not found!"));
//       }
//     } catch (e) {
//       logger.e(e.toString());
//       emit(MasterFilterErrorState(e.toString()));
//     }
//   }

//   Future<String> _mixFinalWavFiles() async {
//     _deletePreviousMixFile();

//     JuceMixer juceMixer = JuceMixer();
//     final outDuration = await juceMixer.getAudioFileDuration(finalVocalFilePath);

//     MixerComposeModel juceComposeModel = MixerComposeModel(tracks: [
//       MixerTrack(
//         id: MixerTrackType.vocal.createString(),
//         path: finalVocalFilePath,
//         volume: finalVocalVolume,
//         offset: vocalfiltersBloc.vocalOffset,
//         fromTime: vocalfiltersBloc.vocalFromTime,
//       ),
//       MixerTrack(
//         id: MixerTrackType.bgm.createString(),
//         path: bgmWavPath,
//         volume: finalBgmVolume,
//       ),
//     ], output: juceMasterMixOutput, outputDuration: outDuration);

//     await juceMixer.export(juceComposeModel);
//     juceMixer.dispose();
//     logger.d("_mix Final Wav Files mix offline complete ✅ $juceMasterMixOutput");

//     await _startFilterProcessingQueue();

//     MasterFilterItem? selectedFilter = _getSelectedMasterFilter();
//     if (selectedFilter?.fileURL == null || selectedFilter!.isLoading) {
//       logger.d("_mixWavFiles file missing");
//       return "Audio file not found";
//     }
//     return "";
//   }

//   Future<void> createFilterItems() async {
//     const List<(String, String, String)> fileNames = [
//       ("acoustic_master.wav", "Acoustic", "acoustic"),
//       ("rock_master.wav", "Rock", "rock"),
//       ("lofi_master.wav", "LoFi", "lofi"),
//       ("pop_master.wav", "Pop", "pop"),
//       ("elec_master.wav", "Electronic", "elec"),
//     ];

//     String output = await _getJuceDir();
//     _filters.add(MasterFilterItem(
//       title: normalFilter,
//       fileURL: juceMasterMixOutput,
//       isLoading: false,
//       thumbnailPath: "${AssetPaths.filterIcons}/normal.png",
//     ));

//     for (var i = 0; i < fileNames.length; i++) {
//       var (fileName, title, alias) = fileNames[i];
//       var item = MasterFilterItem(
//         title: title,
//         fileURL: "$output$fileName",
//         thumbnailPath: "${AssetPaths.filterIcons}/$alias.png",
//         isLoading: true,
//       );
//       _filters.add(item);
//     }
//   }

//   Future<void> _startFilterProcessingQueue() async {
//     try {
//       try {
//         var filter = _filters.firstWhere((e) => e.title == normalFilter);
//         filter.fileURL = juceMasterMixOutput;
//         filter.isLoading = false;
//         emit(MasterFilterItemUpdated());
//       } catch (_) {}

//       // create output dir and clear it
//       String output = await _getJuceDir();
//       logger.i("🔷 output $output");
//       Directory outputDir = Directory(output);
//       try {
//         if (outputDir.existsSync()) {
//           await outputDir.delete(recursive: true);
//         }
//       } catch (_) {}
//       await outputDir.create();

//       String configPath = await AssetPaths.extractAsset("assets/juce_configs/config_master.json");
//       String genreMapPath = await AssetPaths.extractAsset("assets/juce_configs/genre_map.json");

//       await Future.delayed(Duration(milliseconds: 1500));
//       logger.i("🔷 start juce");
//       await juce.applyProcessingGraphMaster(
//         juceMasterMixOutput,
//         output,
//         configPath,
//         genreMapPath,
//         shareDataBloc.annotatedData!.genre,
//         (file) {
//           try {
//             var filter = _filters.firstWhere((e) => e.fileURL == file);
//             filter.isLoading = false;
//             emit(MasterFilterItemUpdated());
//           } catch (_) {}
//         },
//       );
//       logger.i("juce applyProcessingGraphMaster complete ✅ $output");
//       _getFilters().forEach((e) => e.isLoading = false);

//       emit(MasterFilterItemUpdated());
//     } catch (error) {
//       logger.e("juce error: $error");
//       emit(MasterFilterErrorState(error.toString()));
//     }
//   }

// // Juce Helper Methods

//   FutureOr<void> _selectFilter(MasterPageSelectFilter event, _) {
//     selectedFilterIndex = event.selectedFilterIndex;
//     emit(MasterFilterItemUpdated());
//   }

//   MasterFilterItem? _getSelectedMasterFilter() {
//     MasterFilterItem selectedFilter = getFilters()[selectedFilterIndex];
//     masterFilterName = selectedFilter.title;
//     return selectedFilter;
//   }

//   Future<String> _getJuceDir() async {
//     final documentsDirpath = await PathWrapper.getApplicationDocumentsDirectoryPath();
//     return "$documentsDirpath/juce_out_master/";
//   }

//   Future<String> _getMixWavOutputDirectory() async {
//     final documentsDirpath = await PathWrapper.getApplicationDocumentsDirectoryPath();
//     return "$documentsDirpath/master_out";
//   }

//   List<MasterFilterItem> _getFilters() {
//     List<MasterFilterItem> list = _filters.toList();
//     return list;
//   }

//   List<MasterFilterItem> getFilters() {
//     List<MasterFilterItem> list = _filters.toList();
//     return list;
//   }

//   void _deletePreviousMixFile() async {
//     var mixFilePath = previousJuceComposeModel?.output;
//     if (mixFilePath != null) {
//       try {
//         if (File(mixFilePath).existsSync()) {
//           File(mixFilePath).deleteSync();
//         }
//       } catch (_) {}
//     }
//   }

//   Future<void> _cleanMixOutputDirectory() async {
//     try {
//       String dir = await _getMixWavOutputDirectory();
//       if (Directory(dir).existsSync()) {
//         Directory(dir).deleteSync(recursive: true);
//       }
//     } catch (_) {}
//   }
// }


//   // Future<void> _mixWavFiles(MasterFilterEditChanged event) async {
//   //   logger.d("_mixWavFiles $event");
//   //   currentEvent = event;
//   //   _deletePreviousMixFile();

//   //   MasterFilterItem? selected = _getSelectedVocalFilter();
//   //   if (selected?.fileURL == null || selected!.isLoading) {
//   //     logger.d("_mixWavFiles file missing");
//   //     emit(const MasterFilterPlayerErrorState("Audio file not found!"));
//   //     return;
//   //   }

//   //   final juceModel = JuceOfflineMixModel(
//   //     vocal_file_path: selected.fileURL!,
//   //     vocal_delay_ms: 0,
//   //     vocal_volume: vocalVolume,
//   //     music_file_path: bgmWavPath,
//   //     music_delay_ms: 0,
//   //     music_volume: bgmVolume,
//   //     mix_file_path: previousMixModel?.mix_file_path ?? '',
//   //   );

//   //   if (previousMixModel != null) {
//   //     if (juceModel == previousMixModel) {
//   //       logger.d("_mix Wav Files same model");
//   //       return;
//   //     }
//   //   }

//   //   emit(const MasterFilterLoadedState(isLoading: true));
//   //   masterFiltersAudioPlayerBloc.add(AudioPlayerPauseEvent());

//   //   final dirPath = await _getMixWavOutputDirectory();
//   //   final juceFinalMixOutput = "$dirPath/${DateTime.now().millisecondsSinceEpoch}.wav";
//   //   if (!Directory(dirPath).existsSync()) {
//   //     await Directory(dirPath).create();
//   //   }

//   //   previousMixModel = juceModel.copyWith(mix_file_path: juceFinalMixOutput);
//   //   currentMixofflinePath = juceFinalMixOutput;

//   //   await juce.mixOffline(model: previousMixModel!);

//   //   if (currentMixofflinePath != juceFinalMixOutput) {
//   //     return;
//   //   }
//   //   if (File(juceFinalMixOutput).existsSync()) {
//   //     logger.d("_mix Wav Files success set $juceFinalMixOutput");
//   //     masterFiltersAudioPlayerBloc.add(AudioPlayerLoadEvent(
//   //       annotationId: "",
//   //       songId: "",
//   //       path: juceFinalMixOutput,
//   //       type: AudioType.file,
//   //       autoPlay: true,
//   //     ));
//   //     emit(const MasterFilterLoadedState(isLoading: false));
//   //   } else {
//   //     logger.d("_mix Wav Files failed $juceFinalMixOutput");
//   //     emit(const MasterFilterPlayerErrorState("File not found!"));
//   //   }
//   // }