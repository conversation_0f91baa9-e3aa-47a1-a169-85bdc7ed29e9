// // ignore_for_file: public_member_api_docs, sort_constructors_first
// import 'dart:async';

// import 'package:auto_route/auto_route.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:melodyze/core/navigation/app_router.dart';
// import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
// import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
// import 'package:melodyze/core/ui/atom/app_slider.dart';
// import 'package:melodyze/core/ui/molecules/butons/transparent_round_icon_button.dart';
// import 'package:melodyze/core/ui/molecules/custom_vertical_slider.dart';
// import 'package:melodyze/core/ui/molecules/dialoges.dart';
// import 'package:melodyze/core/ui/molecules/filter_item.dart';
// import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
// import 'package:melodyze/core/ui/tokens/app_colors.dart';
// import 'package:melodyze/core/ui/tokens/app_fonts.dart';
// import 'package:melodyze/core/ui/tokens/app_gradients.dart';
// import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
// import 'package:melodyze/core/ui/tokens/asset_paths.dart';
// import 'package:melodyze/core/wrappers/app_toast.dart';
// import 'package:melodyze/core/wrappers/image_loader.dart';
// import 'package:melodyze/core/wrappers/injector.dart';
// import 'package:melodyze/modules/master_filters/bloc/master_filters_audio_player_bloc.dart';
// import 'package:melodyze/modules/master_filters/bloc/master_filters_bloc.dart';
// import 'package:melodyze/modules/master_filters/master_filter_item.dart';
// import 'package:melodyze/modules/share/share_data_bloc.dart';
// import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
// import 'package:melodyze/modules/vocal_filters/bloc/vocal_filters_bloc.dart';

// @RoutePage()
// class MasterFiltersScreen extends StatelessWidget {
//   final String finalVocalFilePath;
//   final String bgmWavPath;
//   final double finalBgmVolume;
//   final double finalVocalVolume;
//   final VocalfiltersBloc vocalfiltersBloc;
//   final MasterFiltersAudioPlayerBloc masterFilterAudioPlayerBloc;

//   const MasterFiltersScreen(
//       {super.key,
//       required this.finalVocalFilePath,
//       required this.bgmWavPath,
//       required this.finalBgmVolume,
//       required this.finalVocalVolume,
//       required this.vocalfiltersBloc,
//       required this.masterFilterAudioPlayerBloc});

//   @override
//   Widget build(BuildContext context) {
//     return MultiBlocProvider(
//       providers: [
//         BlocProvider(
//             create: (context) => MasterFiltersBloc(
//                   masterFiltersAudioPlayerBloc: masterFilterAudioPlayerBloc,
//                   shareDataBloc: context.read<ShareDataBloc>(),
//                   finalVocalFilePath: finalVocalFilePath,
//                   bgmWavPath: bgmWavPath,
//                   finalBgmVolume: finalBgmVolume,
//                   finalVocalVolume: finalVocalVolume,
//                   vocalfiltersBloc: vocalfiltersBloc,
//                 )),
//         BlocProvider(create: (context) => masterFilterAudioPlayerBloc),
//         BlocProvider.value(
//           value: vocalfiltersBloc,
//         ),
//       ],
//       child: _MasterFiltersScreen(
//         finalVocalVolume: finalVocalVolume,
//         finalBgmVolume: finalBgmVolume,
//       ),
//     );
//   }
// }

// class _MasterFiltersScreen extends StatelessWidget {
//   final double finalBgmVolume;
//   final double finalVocalVolume;

//   const _MasterFiltersScreen({
//     required this.finalVocalVolume,
//     required this.finalBgmVolume,
//   });

//   void onVolumeChange(BuildContext context, double value, {required bool isVocal}) {
//     final masterFiltersBloc = context.read<MasterFiltersBloc>();
//     isVocal ? masterFiltersBloc.changeVocalVolume(value) : masterFiltersBloc.changeBgmVolume(value);
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       context.read<MasterFiltersBloc>().add(MasterFilterEditChanged());
//     });
//   }

//   @override
//   Widget build(BuildContext context) {
//     return MeloScaffold(
//       // popScopeDescription: 'Are you sure you want to discard the recording?',
//       onPopScopeDiscard: () => context.read<MasterFiltersBloc>().add(MasterPageStopEvent()),
//       showBackground: false,
//       secondaryAction: (context) => BlocBuilder<MasterFiltersBloc, MasterFiltersState>(
//         buildWhen: (previous, current) => current is! MasterFilterItemUpdated,
//         builder: (context, state) {
//           if (state is MasterFilterInitialState || state is MasterFilterLoadingState) {
//             return SizedBox();
//           }
//           return GestureDetector(
//             onTap: () => _onSave(context),
//             child: Padding(
//               padding: const EdgeInsets.only(right: 8.0),
//               child: Column(
//                 children: [
//                   ImageLoader.fromAsset(
//                     AssetPaths.saveButton,
//                     height: 20,
//                   ),
//                   Text(
//                     "Save",
//                     style: AppTextStyles.text14regular.copyWith(
//                       fontFamily: AppFonts.iceland,
//                     ),
//                   )
//                 ],
//               ),
//             ),
//           );
//         },
//       ),
//       body: SafeArea(
//           child: BlocConsumer<MasterFiltersBloc, MasterFiltersState>(
//               listener: (context, state) {},
//               buildWhen: (previous, current) => current is! MasterFilterItemUpdated,
//               builder: (context, state) {
//                 if (state is MasterFilterInitialState || state is MasterFilterLoadingState) {
//                   return const Center(
//                     child: AppCircularProgressIndicator(),
//                   );
//                 }
//                 if (state is MasterFilterLoadedState || state is MasterFilterPlayerErrorState) {
//                   return Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       const SizedBox(
//                         height: 16,
//                       ),
//                       state is MasterFilterLoadedState
//                           ? Expanded(
//                               child: ClipRRect(
//                                 borderRadius: BorderRadius.circular(16),
//                                 child: Stack(
//                                   children: [
//                                     Positioned(
//                                       left: 0,
//                                       right: 0,
//                                       top: 0,
//                                       bottom: 0,
//                                       child: BlocBuilder<MasterFiltersAudioPlayerBloc, AudioPlayerState>(
//                                         buildWhen: (previous, current) =>
//                                             (current is AudioPlayerPlaying || current is AudioPlayerLoaded || current is AudioPlayerPaused || current is AudioPlayerStopped) &&
//                                             current is! VocalFilterItemUpdated,
//                                         builder: (context, state) {
//                                           return ImageLoader.fromAsset(
//                                             state is AudioPlayerPlaying ? AssetPaths.catRoundEqualizer : AssetPaths.catRoundEqualizerStatic,
//                                           );
//                                         },
//                                       ),
//                                     ),
//                                     Stack(
//                                       children: [
//                                         Positioned(
//                                           left: 8,
//                                           bottom: 68,
//                                           top: 100,
//                                           child: CustomVerticalSlider(
//                                             value: finalVocalVolume,
//                                             max: 1,
//                                             label: 'Vocal',
//                                             color: AppColors.pink,
//                                             alignment: SliderAlignment.left,
//                                             onChanged: (value) => onVolumeChange(context, value, isVocal: true),
//                                           ),
//                                         ),
//                                         Positioned(
//                                           right: 8,
//                                           bottom: 68,
//                                           top: 100,
//                                           child: CustomVerticalSlider(
//                                             value: finalBgmVolume,
//                                             max: 1,
//                                             label: 'BGM',
//                                             color: AppColors.pink2,
//                                             alignment: SliderAlignment.right,
//                                             onChanged: (value) => onVolumeChange(context, value, isVocal: false),
//                                           ),
//                                         ),
//                                         Positioned(
//                                           left: 62,
//                                           right: 62,
//                                           bottom: 62,
//                                           top: 100,
//                                           child: state.isLoading
//                                               ? const Center(
//                                                   child: SizedBox(
//                                                     height: 60,
//                                                     width: 60,
//                                                     child: AppCircularProgressIndicator(),
//                                                   ),
//                                                 )
//                                               : const SizedBox.shrink(),
//                                         ),
//                                         Positioned(
//                                           bottom: 20,
//                                           left: 8,
//                                           right: 8,
//                                           child: Row(
//                                             children: [
//                                               BlocBuilder<MasterFiltersAudioPlayerBloc, AudioPlayerState>(
//                                                 buildWhen: (previous, current) =>
//                                                     (current is AudioPlayerPlaying ||
//                                                         current is AudioPlayerLoaded ||
//                                                         current is AudioPlayerPaused ||
//                                                         current is AudioPlayerStopped) &&
//                                                     current is! VocalFilterItemUpdated,
//                                                 builder: (context, state) {
//                                                   return Padding(
//                                                     padding: const EdgeInsets.only(right: 8.0),
//                                                     child: AppGradientContainer(
//                                                       gradient: AppGradients.gradientPinkPurpleTransparent,
//                                                       borderGradient: AppGradients.gradientPinkBorder,
//                                                       borderRadius: BorderRadius.circular(32),
//                                                       borderWidth: 1,
//                                                       height: 36,
//                                                       width: 36,
//                                                       shape: BoxShape.circle,
//                                                       child: TransParentRoundIconButton(
//                                                         icon: state is AudioPlayerPlaying ? Icons.pause : Icons.play_arrow,
//                                                         backgroundColor: Colors.transparent,
//                                                         onPressed: () {
//                                                           state is AudioPlayerPlaying
//                                                               ? context.read<MasterFiltersBloc>().add(MasterPagePauseEvent())
//                                                               : context.read<MasterFiltersBloc>().add(MasterPagePlayEvent());
//                                                         },
//                                                       ),
//                                                     ),
//                                                   );
//                                                 },
//                                               ),
//                                               BlocBuilder<MasterFiltersAudioPlayerBloc, AudioPlayerState>(
//                                                 buildWhen: (previous, current) =>
//                                                     (current is AudioPlayerProgressUpdated || current is AudioPlayerLoaded || current is AudioPlayerError) &&
//                                                     current is! MasterFilterItemUpdated,
//                                                 builder: (context, state) {
//                                                   if (state is AudioPlayerError) {
//                                                     return AppSlider(
//                                                       value: 0,
//                                                       max: 100,
//                                                       onChanged: (value) {},
//                                                     );
//                                                   }
//                                                   final masterFilterAudioPlayerBloc = context.read<MasterFiltersAudioPlayerBloc>();

//                                                   final double vocalMaxDuration = masterFilterAudioPlayerBloc.audioDuration.inMilliseconds.toDouble();
//                                                   final double vocalPosition = masterFilterAudioPlayerBloc.position.inMilliseconds.toDouble();

//                                                   return Expanded(
//                                                     child: AppSlider(
//                                                       value: state is AudioPlayerProgressUpdated ? vocalPosition : 0,
//                                                       max: vocalMaxDuration,
//                                                       onChanged: (value) {
//                                                         context.read<MasterFiltersBloc>().add(MasterPageSeekEvent(milliseconds: value));
//                                                       },
//                                                       activeColor: AppColors.pink2,
//                                                       thumbRadius: 0.0,
//                                                       trackHeight: 1,
//                                                     ),
//                                                   );
//                                                 },
//                                               ),
//                                             ],
//                                           ),
//                                         ),
//                                       ],
//                                     ),
//                                   ],
//                                 ),
//                               ),
//                             )
//                           : const Text('Something went wrong!'),
//                       const SizedBox(
//                         height: 16,
//                       ),
//                       createFiltersView(context, state),
//                     ],
//                   );
//                 }
//                 if (state is MasterFilterErrorState) {
//                   return Center(
//                     child: Text(state.error.toString()),
//                   );
//                 }
//                 return const SizedBox.shrink();
//               })),
//     );
//   }

//   Widget createFiltersView(BuildContext context, MasterFiltersState state) {
//     return BlocBuilder<MasterFiltersBloc, MasterFiltersState>(
//         buildWhen: (previous, current) => current is MasterFilterItemUpdated,
//         builder: (context, state) {
//           MasterFiltersBloc bloc = context.read<MasterFiltersBloc>();
//           List<MasterFilterItem> filterItems = bloc.getFilters();
//           return Row(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Flexible(
//                 child: ClipRRect(
//                   borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), bottomLeft: Radius.circular(16)),
//                   child: Container(
//                     height: 125,
//                     color: AppColors.black010101_25,
//                     child: ListView.separated(
//                       padding: const EdgeInsets.fromLTRB(8, 0, 16, 0),
//                       scrollDirection: Axis.horizontal,
//                       itemBuilder: (context, index) => FilterItem(
//                         title: filterItems[index].title,
//                         thumbnailPath: filterItems[index].thumbnailPath,
//                         showLoading: filterItems[index].isLoading,
//                         isSelected: bloc.selectedFilterIndex == index,
//                         onTap: () {
//                           bloc.add(MasterPageSelectFilter(selectedFilterIndex: index));
//                           bloc.add(MasterFilterEditChanged());
//                           DI().resolve<AppToast>().showToast("${filterItems[index].title} applied");
//                         },
//                       ),
//                       separatorBuilder: ((context, index) => const SizedBox(width: 8)),
//                       itemCount: filterItems.length,
//                     ),
//                   ),
//                 ),
//               ),
//             ],
//           );
//         });
//   }

//   void _onSave(BuildContext context) async {
//     final result = await showYesNoDialog(
//       context: context,
//       title: 'Save and Upload',
//       subTitle: 'Are you sure you want to save and upload this recording?',
//     );

//     if (result && context.mounted) {
//       context.read<MasterFiltersAudioPlayerBloc>().add(AudioPlayerStopEvent());
//       // unawaited(
//       //   context.pushRoute(
//       //     SaveAndUploadRoute(
//       //       masterFiltersBloc: context.read<MasterFiltersBloc>(),
//       //       vocalfiltersBloc: context.read<VocalfiltersBloc>(),
//       //       shareDataBloc: context.read<ShareDataBloc>(),
//       //     ),
//       //   ),
//       // );
//     }
//   }
// }
