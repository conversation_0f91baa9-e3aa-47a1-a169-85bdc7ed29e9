import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_arch/base_service.dart';
// import 'package:melodyze/core/services/aws_client.dart';
import 'package:melodyze/core/wrappers/injector.dart';

class SongPersonalizationService extends BaseService {
  Future<Map<String, dynamic>?> getAllAnnotation({required String songId}) async {
    return await DI().resolve<ApiClient>().post(
      Endpoints.getAllAnnotation,
      body: {"master_song_id": songId},
    );
  }

  Future<Map<String, dynamic>?> getLyrics({required String lyricsPath}) async {
    // final signedUrl = await DI().resolve<AwsClient>().getSignedUrl(lyricsPath);
    return await DI().resolve<ApiClient>().get(lyricsPath);
  }
}
