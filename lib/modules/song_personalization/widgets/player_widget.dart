import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/config/config.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/atom/app_slider.dart';
import 'package:melodyze/core/ui/atom/grayscaled_filter.dart';
import 'package:melodyze/core/ui/atom/marquee.dart';
import 'package:melodyze/core/ui/molecules/butons/transparent_round_icon_button.dart';
import 'package:melodyze/core/ui/molecules/knob.dart';
import 'package:melodyze/core/ui/molecules/network_image.dart';
import 'package:melodyze/core/ui/molecules/silder_with_time.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/song_personalization_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/song_personalization_event.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';

class PlayerWidget extends StatelessWidget {
  final AnnotatedData annotatedData;
  final SongModel song;
  const PlayerWidget({
    super.key,
    required this.annotatedData,
    required this.song,
  });

  // @override
  @override
  Widget build(BuildContext context) {
    final songPersonalizationBloc = context.read<SongPersonalizationBloc>();
    return AppGradientContainer(
      gradient: AppGradients.gradientDarkPurpleBackground,
      borderGradient: AppGradients.gradientPinkBlueBorder,
      borderRadius: BorderRadius.circular(36),
      borderWidth: 3,
      enableBlur: true,
      enableForegroundDecoration: true,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(30),
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                foregroundDecoration: BoxDecoration(color: Color(0xA6000000)),
                child: GrayscaledFilter(
                  child: NetworkImageWidget(
                    imageUrl: song.thumbnailPath,
                  ),
                ),
              ),
            ),
            BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
              child: Container(
                color: AppColors.black010101_50,
              ),
            ),
            Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 18),
                Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: Row(
                    children: [
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 24),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Marqueee(
                                text: annotatedData.genre.capitalizeFirst(),
                                style: AppTextStyles.text26semiBold.copyWith(fontFamily: AppFonts.inter),
                                width: 320,
                              ),
                              Marqueee(
                                text: '${song.title} • ${song.singer}',
                                style: AppTextStyles.text16medium.copyWith(fontFamily: AppFonts.iceland),
                                width: 250,
                              )
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8.0),
                Row(
                  children: [
                    const SizedBox(
                      width: 18,
                    ),
                    BlocConsumer<AudioPlayerBloc, BlocState>(
                      listenWhen: (previous, current) => current is AudioPlayerError,
                      listener: (context, state) {
                        if (state is AudioPlayerError) {
                          DI().resolve<AppToast>().showToast(state.error.toString());
                        }
                      },
                      buildWhen: (previous, current) =>
                          current is AudioPlayerLoaded ||
                          current is AudioPlayerPlaying ||
                          current is AudioPlayerPaused ||
                          current is AudioPlayerStopped ||
                          current is AudioPlayerError,
                      builder: (context, state) {
                        return AppGradientContainer(
                          height: 40,
                          width: 40,
                          gradient: AppGradients.gradientPinkBackground,
                          borderGradient: AppGradients.gradientPinkBorder,
                          borderRadius: BorderRadius.circular(32),
                          borderWidth: 2,
                          shape: BoxShape.circle,
                          child: TransParentRoundIconButton(
                            icon: state is AudioPlayerPlaying ? Icons.pause : Icons.play_arrow,
                            isDisabled: state is AudioPlayerError,
                            backgroundColor: Colors.transparent,
                            onPressed: () {
                              context.read<AudioPlayerBloc>().isPlaying
                                  ? context.read<AudioPlayerBloc>().add(AudioPlayerPauseEvent())
                                  : context.read<AudioPlayerBloc>().add(AudioPlayerPlayEvent());
                            },
                          ),
                        );
                      },
                    ),
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 16.0),
                        child: BlocBuilder<AudioPlayerBloc, BlocState>(
                            buildWhen: (previous, current) => current is AudioPlayerProgressUpdated || current is AudioPlayerLoaded || current is AudioPlayerError,
                            builder: (context, state) {
                              if (state is AudioPlayerError) {
                                return AppSlider(
                                  value: 0,
                                  max: 100,
                                  trackHeight: 1,
                                  onChanged: (value) {},
                                );
                              }
                              final audioPlayerBloc = context.read<AudioPlayerBloc>();
                              final double maxDuration = audioPlayerBloc.audioDuration.inMilliseconds.toDouble();
                              final double position = audioPlayerBloc.position.inMilliseconds.toDouble();
                              return SliderWithTime(
                                value: state is AudioPlayerProgressUpdated ? position : 0,
                                max: maxDuration,
                                onChanged: (value) {
                                  audioPlayerBloc.add(AudioPlayerSeekEvent(milliseconds: value));
                                },
                                showTimer: false,
                              );
                            }),
                      ),
                    ),
                    const SizedBox(
                      width: 24,
                    )
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Visibility(
                      maintainSize: true,
                      maintainAnimation: true,
                      maintainState: true,
                      visible: songPersonalizationBloc.isResetVisible(),
                      child: IconButton(
                        onPressed: () {
                          songPersonalizationBloc.add(ResetSettingsEvent(genre: annotatedData.genre));
                        },
                        icon: const Icon(
                          Icons.refresh,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 84,
                      child: Knob(
                        initialValue: annotatedData.scale,
                        data: songPersonalizationBloc.scales,
                        labels: Config.keyMap,
                        onChanged: (value) {
                          HapticFeedback.mediumImpact();
                          //This is a workaround to avoid the knob to be updated when the user is not on the current song
                          //For a better solution, we need to use a cubit to manage the player state and the knob state
                          if (context.read<SongPersonalizationBloc>().currentAnnotatedData == annotatedData) {
                            songPersonalizationBloc.add(ChangeSettingsEvent(scale: value, source: AudioPlaySource.scaleKnob));
                            // audioPlayerBloc.add(AudioPlayerSeekEvent(milliseconds: currentSeekPosition.toDouble()));
                          }
                        },
                      ),
                    ),
                    const SizedBox(
                      width: 16,
                    ),
                    SizedBox(
                      height: 84,
                      child: Knob(
                        initialValue: annotatedData.tempo,
                        data: songPersonalizationBloc.tempos[songPersonalizationBloc.currentGenreIndex],
                        labels: CommonUtils.getTempoSpeedMap(songPersonalizationBloc.tempos[songPersonalizationBloc.currentGenreIndex]),
                        onChanged: (value) {
                          HapticFeedback.mediumImpact();
                          //This is a workaround to avoid the knob to be updated when the user is not on the current song
                          //For a better solution, we need to use a cubit to manage the player state and the knob state
                          if (context.read<SongPersonalizationBloc>().currentAnnotatedData == annotatedData) {
                            songPersonalizationBloc.add(ChangeSettingsEvent(tempo: value, source: AudioPlaySource.tempoKnob));
                          }
                        },
                      ),
                    ),
                    // Size Box of 48 added to set those knobs in center
                    const SizedBox(height: 48, width: 48)
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
