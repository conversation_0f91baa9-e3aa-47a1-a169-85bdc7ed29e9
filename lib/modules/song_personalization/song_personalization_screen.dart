import 'dart:io';

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/navigation/app_router.dart';
import 'package:melodyze/core/ui/atom/app_circular_progress_indicator.dart';
import 'package:melodyze/core/ui/atom/bottom_gradient_black.dart';
import 'package:melodyze/core/ui/molecules/butons/app_button.dart';
import 'package:melodyze/core/ui/molecules/expandable_swipe_widget.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/lyrics_viewer.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/core/ui/molecules/navigation/melo_scaffold.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/ui/tokens/app_text_styles.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/modules/feed/model/feed_request_type.dart';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/song_personalization_bloc.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';
import 'package:melodyze/modules/song_personalization/repo/song_personalization_repo.dart';
import 'package:melodyze/modules/song_personalization/service/song_personalization_service.dart';
import 'package:melodyze/modules/song_personalization/widgets/player_widget.dart';

@RoutePage()
class SongPersonalizationScreen extends StatelessWidget {
  final SongModel song;
  final String? defaultGenre;
  const SongPersonalizationScreen({super.key, required this.song, this.defaultGenre});

  @override
  Widget build(BuildContext context) {
    final songPersonalizationRepo = SongPersonalizationRepo(
      songPersonalizationService: SongPersonalizationService(),
    );
    final lyricsViewerBloc = LyricsViewerBloc(songPersonalizationRepo: songPersonalizationRepo);
    final audioPlayerBloc = AudioPlayerBloc();
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) {
            return SongPersonalizationBloc(
              song: song,
              songPersonalizationRepo: songPersonalizationRepo,
              audioPlayerBloc: audioPlayerBloc,
              lyricsViewerBloc: lyricsViewerBloc,
              defaultGenre: defaultGenre,
            );
          },
        ),
        BlocProvider(create: (context) => lyricsViewerBloc),
        BlocProvider(create: (context) => audioPlayerBloc),
      ],
      child: _SongPersonalizationScreen(),
    );
  }
}

class _SongPersonalizationScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MeloScaffold(
      showBackground: false,
      overlayAction: Positioned(
        top: MediaQuery.of(context).padding.top,
        child: Center(
          child: BlocBuilder<SongPersonalizationBloc, BlocState>(
            buildWhen: (previous, current) => current is BlocSuccessState<List<AnnotatedData>>,
            builder: (context, state) {
              return BlocBuilder<AudioPlayerBloc, AudioPlayerState>(
                buildWhen: (previous, current) => current is AudioPlayerLoaded || current is AudioPlayerError || current is AudioPlayerInitial,
                builder: (context, state) {
                  final songPersonalizationBloc = context.read<SongPersonalizationBloc>();
                  final guideVocalPath = songPersonalizationBloc.currentAnnotatedData?.guideVocalPath;

                  return ExpandableSwipeWidget(
                    isLoading: state is AudioPlayerInitial,
                    items: [
                      ExpandableSwipeWidgetModel(
                        title: "Guide",
                        label: "Guide",
                        iconPath: AssetPaths.guide,
                        isDisabled: context.read<AudioPlayerBloc>().tracks[MixerTrackType.guide] == null || state is AudioPlayerError,
                        onVolumeChanged: guideVocalPath != null
                            ? (value) {
                                context.read<AudioPlayerBloc>().add(
                                      EnableGuideTrackEvent(
                                        guideS3Path: guideVocalPath,
                                        bgmS3Path: songPersonalizationBloc.currentAnnotatedData!.songPath,
                                        volume: value,
                                      ),
                                    );
                              }
                            : null,
                        onPressed: guideVocalPath != null
                            ? (isEnabled) {
                                if (isEnabled) {
                                  context.read<AudioPlayerBloc>().add(DisableGuideTrackEvent());
                                } else {
                                  context.read<AudioPlayerBloc>().add(EnableGuideTrackEvent(
                                        guideS3Path: guideVocalPath,
                                        bgmS3Path: songPersonalizationBloc.currentAnnotatedData!.songPath,
                                      ));
                                }
                              }
                            : null,
                      ),
                      ExpandableSwipeWidgetModel(
                        title: "Metronome",
                        label: "Click",
                        iconPath: AssetPaths.metronome,
                        isDisabled: context.read<AudioPlayerBloc>().metronomeTracks.isEmpty || state is AudioPlayerError,
                        onVolumeChanged: (value) {
                          context.read<AudioPlayerBloc>().add(
                                EnableMetronomeEvent(
                                  bgmS3Path: songPersonalizationBloc.currentAnnotatedData!.songPath,
                                  timeSignature: songPersonalizationBloc.song.timeSignature,
                                  volume: value,
                                  tempo: int.tryParse(songPersonalizationBloc.currentAnnotatedData!.tempo) ?? 0,
                                ),
                              );
                        },
                        onPressed: (isEnabled) {
                          if (isEnabled) {
                            context.read<AudioPlayerBloc>().add(DisableMetronomeEvent());
                          } else {
                            context.read<AudioPlayerBloc>().add(
                                  EnableMetronomeEvent(
                                    bgmS3Path: songPersonalizationBloc.currentAnnotatedData!.songPath,
                                    timeSignature: songPersonalizationBloc.song.timeSignature,
                                    tempo: int.tryParse(songPersonalizationBloc.currentAnnotatedData!.tempo) ?? 0,
                                  ),
                                );
                          }
                        },
                      )
                    ],
                  );
                },
              );
            },
          ),
        ),
      ),
      body: SafeArea(
        child: BlocBuilder<SongPersonalizationBloc, BlocState>(builder: (context, state) {
          if (state is LoadingState) {
            return const Center(
              child: AppCircularProgressIndicator(),
            );
          }
          if (state is BlocSuccessState<List<AnnotatedData>>) {
            final songPersonalizationBloc = context.read<SongPersonalizationBloc>();
            return Column(
              children: [
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                    child: Stack(
                      children: [
                        BlocBuilder<LyricsViewerBloc, BlocState>(builder: (context, state) {
                          if (state is LoadingState) {
                            return const Center(
                              child: AppCircularProgressIndicator(),
                            );
                          }
                          if (state is BlocSuccessState<LyricsData>) {
                            return BlocBuilder<AudioPlayerBloc, BlocState>(buildWhen: (previous, current) {
                              return current is AudioPlayerProgressUpdated && current.position != Duration.zero;
                            }, builder: (context, playerState) {
                              return RepaintBoundary(
                                child: LyricsViewer(
                                  lyricsData: state.data,
                                  position: playerState is AudioPlayerProgressUpdated ? playerState.position.inMilliseconds : 0,
                                  onLyricTapped: (lineIndex, timestampMillis) => context.read<AudioPlayerBloc>().add(
                                        AudioPlayerSeekEvent(
                                          milliseconds: timestampMillis.toDouble(),
                                        ),
                                      ),
                                ),
                              );
                            });
                          }
                          return const SizedBox.shrink();
                        }),
                        const IgnorePointer(
                          child: Align(
                            alignment: Alignment.bottomCenter,
                            child: BottomGradientBlack(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                AspectRatio(
                  aspectRatio: 16 / 9,
                  child: PageView.builder(
                    scrollDirection: Axis.horizontal,
                    onPageChanged: (value) => songPersonalizationBloc.changeGenreByIndex(value),
                    controller: PageController(
                      viewportFraction: 0.85,
                      initialPage: songPersonalizationBloc.currentGenreIndex,
                    ),
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: PlayerWidget(
                          annotatedData: state.data[index],
                          song: songPersonalizationBloc.song,
                        ),
                      );
                    },
                    itemCount: state.data.length,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 38, vertical: 16),
                  child: Row(
                    children: [
                      AppButton(
                        text: 'Listen',
                        iconLeft: AssetPaths.headphone,
                        backgroundColor: Colors.transparent,
                        innerPadding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
                        onPressed: () {
                          context.read<AudioPlayerBloc>().add(AudioPlayerStopEvent());
                          context.pushRoute(
                            FeedRoute(
                                pageName: FeedPageSourceType.personalisation.name,
                                clickedSource: FeedClickedSourceType.listen.name,
                                masterSongId: songPersonalizationBloc.song.id,
                                genre: songPersonalizationBloc.currentAnnotatedData?.genre,
                                genreId: songPersonalizationBloc.currentAnnotatedData?.genreId,
                                lang: songPersonalizationBloc.song.lang),
                          );
                        },
                      ),
                      SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: BlocListener<SongPersonalizationBloc, BlocState>(
                          listenWhen: (previous, current) => current is AudioDownloadComplete,
                          listener: (context, state) {},
                          child: AppButton(
                            text: 'Sing',
                            gradient: AppGradients.gradientPinkBg,
                            gradientDisabled: AppGradients.gradientPinkBgDisabled,
                            borderGradient: AppGradients.gradientPinkBlueBorder,
                            backgroundColor: Colors.transparent,
                            iconLeft: AssetPaths.microphone,
                            innerPadding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 8.0),
                            enabled: context.read<SongPersonalizationBloc>().isAudiosDownloaded,
                            onPressed: () {
                              _onConfirm(context);
                            },
                            textStyle: AppTextStyles.textOutfit900,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            );
          }
          return const SizedBox.shrink();
        }),
      ),
    );
  }

  void _onConfirm(BuildContext context) {
    final songPersonalizationBloc = context.read<SongPersonalizationBloc>();
    final currentAnnotatedData = songPersonalizationBloc.currentAnnotatedData;
    if (currentAnnotatedData == null || currentAnnotatedData.songPath.isNullOrEmpty) {
      DI().resolve<AppToast>().showToast('Please select a song');
      return;
    }
    context.read<AudioPlayerBloc>().add(AudioPlayerStopEvent());
    final lyricsViewerBloc = context.read<LyricsViewerBloc>();
    final audioPlayerBloc = context.read<AudioPlayerBloc>();
    audioPlayerBloc.add(AudioPlayerStopEvent());

    /**
   * TODO: Remove this hacky disable metronome and guide track code
   */
    if (Platform.isIOS) {
      audioPlayerBloc.metronomeTracks.clear();
      audioPlayerBloc.tracks.remove(MixerTrackType.guide);
    }

    context.read<ShareDataBloc>().add(ShareDataEvent(
      annotatedData: currentAnnotatedData, 
      song: songPersonalizationBloc.song, 
      guidePath: songPersonalizationBloc.downloadedGuidePath,
      bgmPath: songPersonalizationBloc.downloadedBgmPath,
    ));
    context.pushRoute(
      RecordingRoute(
        audioPlayerBloc: audioPlayerBloc,
        lyricsViewerBloc: lyricsViewerBloc,
      ),
    );
  }
}
