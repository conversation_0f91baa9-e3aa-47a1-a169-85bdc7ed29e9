import 'dart:async';

// ignore: depend_on_referenced_packages
import 'package:collection/collection.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';

part 'juce_mix_event.dart';
part 'juce_mix_state.dart';

class JuceMixBloc extends SafeBloc<JuceMixEvent, JuceMixState> {
  static const tag = "JuceMixBloc";
  final JuceMixPlayer _juceMixPlayer = JuceMixPlayer();

  MixerComposeModel mixComposeModel;
  bool isMetronomeEnabled = false;
  bool isGuideEnabled = false;
  double metronomeVol = 0.0;
  double guideVol = 0.0;
  double outputDuration = 0.0;
  double currentPosition = 0.0;

  JuceMixBloc({
    required this.mixComposeModel,
  }) : super(JuceMixInit()) {
    on<JuceMixLoadEvent>(_onLoad);
    on<JuceMixAudioInitEvent>(_onAudioInit);
    on<JuceMixAudioPlayEvent>(_onAudioPlay);
    on<JuceMixAudioPauseEvent>(_onAudioPause);
    on<JuceMixAudioTogglePlayPauseEvent>(_onTogglePlayPause);
    on<JuceMixAudioStopEvent>(_onAudioStop);
    on<JuceMixAudioSeekEvent>(_onAudioSeek);
    on<JuceMixToggleMetronomeEvent>(_onToggleMetronome);
    on<JuceMixToggleGuideEvent>(_onToggleGuide);
    on<JuceMixRecordInitEvent>(_onRecordInit);
    on<JuceMixRecordStartEvent>(_onRecordStart);
    on<JuceMixRecordStopEvent>(_onRecordStop);
    add(JuceMixLoadEvent());
  }

  Future<void> exportComposedAudio(String outputFile) async {
    await _juceMixPlayer.export(outputFile);
    logger.d("exportComposedAudio: $outputFile");
  }

  void onSetMixFilter(String vocalFilterId) async {
    logger.i("onSetMixFilter: $vocalFilterId");
    _juceMixPlayer.setVocalFilter(vocalFilterId);
  }

  void onSetMasterFilter(String masterFilterId) async {
    logger.i("_onSetMasterFilter: $masterFilterId");
    _juceMixPlayer.setMasterFilter(masterFilterId);
  }

  void setMixData() async {
    logger.i("onSetMixData: ${mixComposeModel.tracks?.length}");
    _juceMixPlayer.setMixData(mixComposeModel);
  }

  Future<void> _onLoad(JuceMixLoadEvent event, _) async {
    isGuideEnabled = mixComposeModel.tracks?.any((track) => track.id == "guide" && track.enabled == true) ?? false;
    guideVol = mixComposeModel.tracks?.firstWhereOrNull((track) => track.id == "guide")?.volume ?? 1.0;
    isMetronomeEnabled = mixComposeModel.tracks?.any((track) => track.id.startsWith("metronome") && track.enabled == true) ?? false;
    metronomeVol = mixComposeModel.tracks?.firstWhereOrNull((track) => track.id.startsWith("metronome"))?.volume ?? 1.0;
    outputDuration = mixComposeModel.outputDuration ?? 0;
    logger.i("_onLoad :: op: $outputDuration | guide: $isGuideEnabled | met: $isMetronomeEnabled | metVol: $metronomeVol | guideVol: $guideVol");
  }

  Future<void> _onAudioInit(JuceMixAudioInitEvent event, _) async {
    emit(JuceMixAudioLoading());

    _juceMixPlayer.setStateUpdateHandler((state) {
      logger.i("Player state: ${state.toString()}");
      switch (state) {
        case JuceMixPlayerState.IDLE:
          break;
        case JuceMixPlayerState.READY:
          emit(JuceMixAudioLoaded());
          if (event.autoPlay) {
            add(JuceMixAudioPlayEvent());
          }
          break;
        case JuceMixPlayerState.PLAYING:
          emit(JuceMixAudioPlaying());
          break;
        case JuceMixPlayerState.PAUSED:
          emit(JuceMixAudioPaused());
          break;
        case JuceMixPlayerState.STOPPED:
          currentPosition = 0.0;
          emit(JuceMixAudioStopped());
          setMixData();
          break;
        case JuceMixPlayerState.COMPLETED:
          currentPosition = 0.0;
          emit(JuceMixAudioStopped());
          break;
        case JuceMixPlayerState.ERROR:
          emitError("Player error occurred");
          break;
      }
    });

    _juceMixPlayer.setProgressHandler((double progress) {
      final double progressInMillis = progress * outputDuration * 1000;
      currentPosition = progressInMillis;
      emit(JuceMixAudioProgressUpdated(position: progressInMillis));
    });

    _juceMixPlayer.setErrorHandler((error) {
      logger.e('_jucePlayer.setErrorHandler: $error');
      emit(JuceMixAudioError(error: error));
    });
  }

  Future<void> _onAudioPlay(JuceMixAudioPlayEvent event, _) async {
    _juceMixPlayer.play();
  }

  Future<void> _onAudioPause(JuceMixAudioPauseEvent event, _) async {
    _juceMixPlayer.pause();
  }

  Future<void> _onTogglePlayPause(JuceMixAudioTogglePlayPauseEvent event, _) async {
    _juceMixPlayer.togglePlayPause();
  }

  bool get isAudioPlaying {
    return _juceMixPlayer.isPlaying();
  }

  Future<void> _onAudioStop(JuceMixAudioStopEvent event, _) async {
    _juceMixPlayer.stop();
  }

  Future<void> _onAudioSeek(JuceMixAudioSeekEvent event, _) async {
    double totalDurationMs = outputDuration * 1000;
    double seekFraction = event.milliseconds / totalDurationMs;
    seekFraction = seekFraction.clamp(0.0, 1.0);
    currentPosition = event.milliseconds;
    _juceMixPlayer.seek(seekFraction);
  }

  Future<void> _onToggleMetronome(JuceMixToggleMetronomeEvent event, _) async {
    logger.i("_onToggleMetronome: ${event.enable} | ${event.volume}");
    mixComposeModel = mixComposeModel.copyWith(
      tracks: mixComposeModel.tracks?.map((track) {
        if (track.id.startsWith("metronome")) {
          return track.copyWith(enabled: event.enable, volume: event.volume);
        } else {
          return track;
        }
      }).toList(),
    );
    setMixData();
    metronomeVol = event.volume;
    isMetronomeEnabled = event.enable;
  }

  Future<void> _onToggleGuide(JuceMixToggleGuideEvent event, _) async {
    logger.i("_onToggleGuide: ${event.enable} | ${event.volume}");
    mixComposeModel = mixComposeModel.copyWith(
      tracks: mixComposeModel.tracks?.map((track) {
        if (track.id.startsWith("guide")) {
          return track.copyWith(enabled: event.enable, volume: event.volume);
        } else {
          return track;
        }
      }).toList(),
    );
    setMixData();
    guideVol = event.volume;
    isGuideEnabled = event.enable;
  }

  Future<void> _onRecordInit(JuceMixRecordInitEvent event, _) async {
    emit(JuceMixRecordPreparing());
    add(JuceMixLoadEvent());

    _juceMixPlayer.setRecStateUpdateHandler((state) {
      logger.i("Recorder state: ${state.toString()}");

      switch (state) {
        case JuceMixRecState.IDLE:
          break;
        case JuceMixRecState.READY:
          emit(JuceMixRecordPrepared());
          break;
        case JuceMixRecState.RECORDING:
          emit(JuceMixRecordStarted());
          break;
        case JuceMixRecState.STOPPED:
          emit(JuceMixRecordStopped());
          break;
        case JuceMixRecState.ERROR:
          emitError("Recording error occurred");
          break;
      }
    });

    _juceMixPlayer.setRecLevelHandler((level) {
      emit(JuceMixRecordLevelUpdateState(level: level));
      // logger.i("Recorder level: $level");
    });

    _juceMixPlayer.setRecProgressHandler((progress) {
      final progressInMillis = (progress * 1000).toInt();
      emit(JuceMixRecordProgressUpdateState(progressInMillis: progressInMillis));
      // logger.i("Recorder progress: $progressInMillis ms");
    });

    _juceMixPlayer.setErrorHandler((error) {
      logger.e('_juceRecorder.setErrorHandler: $error');
      emit(JuceMixRecordError(error: error));
    });

    _juceMixPlayer.setSettings(MixerSettings());
    _juceMixPlayer.setMixData(mixComposeModel);
    _juceMixPlayer.prepareRecording(event.recordingFilePath);
  }

  Future<void> _onRecordStart(JuceMixRecordStartEvent event, _) async {
    _juceMixPlayer.startRecording();
  }

  Future<void> _onRecordStop(JuceMixRecordStopEvent event, _) async {
    _juceMixPlayer.stopRecording();
  }

  @override
  Future<void> close() async {
    _juceMixPlayer.stop();
    _juceMixPlayer.stopRecording();
    _juceMixPlayer.dispose();
    return super.close();
  }

  void emitError(String message, {dynamic error}) {
    logger.e('$tag: $message', error: error);
    emit(JuceMixError(error: '$message \n ${error ?? ''}'));
  }
}

class JuceMixUtils {
  static final JuceMixer _mixer = JuceMixer();
  static Future<MixerComposeModel> createComposeModel({
    required String bgmPath,
    String vocalPath = '',
    double vocalVol = 1.0,
    double bgmVol = 1.0,
    bool metronomeEnabled = false,
    String timeSign = "",
    int tempo = 0,
    double metronomeVol = 0,
    bool guideEnabled = false,
    String guidePath = "",
    double guideVol = 0,
    double outputDuration = 0,
  }) async {
    logger.i(
        "metronomeEnabled: $metronomeEnabled | metronomeVol: $metronomeVol | tempo: $tempo | timeSign: $timeSign | guideEnabled: $guideEnabled | guidePath: | $guidePath | guideVol: $guideVol");

    List<MixerTrack> tracks = [];
    tracks.add(MixerTrack(id: "bgm", path: bgmPath, enabled: true));
    if (vocalPath.isNotEmpty) {
      tracks.add(MixerTrack(id: "vocal", path: vocalPath, enabled: true, volume: vocalVol));
    }
    final metTracks = _mixer.createMetronomeTracks(
      tempo: tempo,
      timeSignature: timeSign,
      volume: metronomeVol,
      hPath: await AssetPaths.extractAsset("assets/metronome_tone/met_h.wav"),
      lPath: await AssetPaths.extractAsset("assets/metronome_tone/met_l.wav"),
    );
    tracks.addAll(metTracks.map((metTrack) => metTrack.copyWith(enabled: metronomeEnabled)));

    if (guidePath.isNotEmpty) {
      tracks.add(MixerTrack(id: "guide", path: guidePath, enabled: guideEnabled, volume: guideVol));
    }
    return MixerComposeModel(tracks: tracks, outputDuration: outputDuration);
  }
}

extension MixerComposeModelExt on MixerComposeModel {
  List<MixerTrack> get metronomeTracks {
    return tracks?.where((track) => track.id.startsWith("metronome")).toList() ?? [];
  }
}
