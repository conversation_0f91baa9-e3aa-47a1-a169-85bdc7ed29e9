part of 'audio_player_bloc.dart';

enum AudioType { url, file }

enum AudioPlaySource { def, scaleKnob, tempoKnob, vocalFilter, genreSlider }

abstract class AudioPlayerEvent extends BaseEvent {
  const AudioPlayerEvent();
}

class AudioPlayerLoadEvent extends AudioPlayerEvent {
  final String annotationId;
  final String songId;
  final String path;
  final AudioType type;
  final bool autoPlay;
  final AudioPlaySource source;

  const AudioPlayerLoadEvent({
    required this.annotationId,
    required this.songId,
    required this.path,
    required this.type,
    this.autoPlay = false,
    this.source = AudioPlaySource.def,
  });

  @override
  List<Object> get props => [path];
}

class AudioPlayerPlayEvent extends AudioPlayerEvent {
  @override
  List<Object> get props => [];
}

class AudioPlayerPauseEvent extends AudioPlayerEvent {
  @override
  List<Object> get props => [];
}

class AudioPlayerStopEvent extends AudioPlayerEvent {
  @override
  List<Object> get props => [];
}

class AudioPlayerProgressUpdateEvent extends AudioPlayerEvent {
  final Duration position;
  const AudioPlayerProgressUpdateEvent({required this.position});

  @override
  List<Object> get props => [position];
}

class AudioPlayerSeekEvent extends AudioPlayerEvent {
  final double milliseconds;
  const AudioPlayerSeekEvent({required this.milliseconds});

  @override
  List<Object> get props => [milliseconds];
}

class AudioPlayerChangeVolumeEvent extends AudioPlayerEvent {
  final double value;
  const AudioPlayerChangeVolumeEvent(this.value);
  @override
  List<Object?> get props => [value];
}

class EnableMetronomeEvent extends AudioPlayerEvent {
  final String timeSignature;
  final int tempo;
  final double volume;
  final String bgmS3Path;
  final String? vocalFilePath;
  final double bgmVolume;
  final double? vocalVolume;
  final double vocalFromTime;
  final double vocalOffset;

  const EnableMetronomeEvent({
    required this.bgmS3Path,
    required this.timeSignature,
    required this.tempo,
    this.bgmVolume = 1.0,
    this.vocalFilePath,
    this.volume = 1.0,
    this.vocalVolume,
    this.vocalFromTime = 0,
    this.vocalOffset = 0,
  });

  @override
  List<Object?> get props => [timeSignature, tempo];
}

class DisableMetronomeEvent extends AudioPlayerEvent {
  @override
  List<Object?> get props => [];
}

class EnableGuideTrackEvent extends AudioPlayerEvent {
  final String guideS3Path;
  final String bgmS3Path;
  final double volume;
  final String? vocalFilePath;
  final double bgmVolume;
  final double? vocalVolume;
  final double vocalFromTime;
  final double vocalOffset;

  const EnableGuideTrackEvent({
    required this.guideS3Path,
    required this.bgmS3Path,
    this.bgmVolume = 1.0,
    this.volume = 1.0,
    this.vocalFilePath,
    this.vocalVolume,
    this.vocalFromTime = 0,
    this.vocalOffset = 0,
  });

  @override
  List<Object?> get props => [guideS3Path, bgmS3Path, volume];
}

class DisableGuideTrackEvent extends AudioPlayerEvent {
  @override
  List<Object?> get props => [];
}
