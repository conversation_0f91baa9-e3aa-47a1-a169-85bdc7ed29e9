part of 'audio_player_bloc.dart';

abstract class AudioPlayerState extends BlocState {
  const AudioPlayerState();
}

class AudioPlayerInitial extends AudioPlayerState {
  @override
  List<Object> get props => [];
}

class AudioPlayerLoaded extends AudioPlayerState {
  @override
  List<Object> get props => [];
}

class AudioPlayerPlaying extends AudioPlayerState {
  final DateTime startTime;
  const AudioPlayerPlaying({required this.startTime});
  @override
  List<Object> get props => [startTime];
}

class AudioPlayerPaused extends AudioPlayerState {
  @override
  List<Object> get props => [];
}

class AudioPlayerStopped extends AudioPlayerState {
  @override
  List<Object> get props => [];
}

class AudioPlayerProgressUpdated extends AudioPlayerState {
  final Duration position;
  const AudioPlayerProgressUpdated({required this.position});

  @override
  List<Object> get props => [position];
}

class AudioPlayerError extends AudioPlayerState {
  final Object error;
  const AudioPlayerError({required this.error});

  @override
  List<Object> get props => [];
}

class AudioPlayerMetronomeWaiting extends AudioPlayerState {
  @override
  List<Object> get props => [];
}

class AudioPlayerGuideWaiting extends AudioPlayerState {
  @override
  List<Object> get props => [];
}
