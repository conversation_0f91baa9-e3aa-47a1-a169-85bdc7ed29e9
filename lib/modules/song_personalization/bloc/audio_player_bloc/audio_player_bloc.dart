import 'dart:async';
import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'package:just_audio/just_audio.dart';
import 'package:melodyze/core/core_modules/file_downloader/file_downloader.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/recorder/recorder_channel.dart';
// import 'package:melodyze/core/services/aws_client.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/utilities/utils/extensions.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/app_toast.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';
import 'package:melodyze/juce_kit/juce_kit_wrapper.dart';

// Define BLoC events
part 'audio_player_event.dart';
// Define BLoC states
part 'audio_player_state.dart';

enum MixerTrackType {
  bgm,
  guide,
  vocal;

  String createString() {
    return toString().split('.').last;
  }
}

class AudioPlayerBloc extends SafeBloc<AudioPlayerEvent, AudioPlayerState> {
  static const tag = "AudioPlayerBloc";
  final AudioPlayer _audioPlayer = AudioPlayer();
  AppLifecycleListener? _listener;
  Duration audioDuration = Duration.zero;
  bool get isPlaying => _audioPlayer.playing;
  JuceMixer mixer = JuceMixer();
  Map<MixerTrackType, MixerTrack> tracks = {};
  List<MixerTrack> metronomeTracks = [];
  String? mixedAudioPath;
  String? downloadedBgmPath;
  String? downloadedGuidePath;
  AudioPlayerLoadEvent? lastLoadEvent;
  String? lastAnnotationId;
  String? lastSongId;
  RecorderChannel? record;
  bool operationInProgress = false;

  /// Use [position] for with widgets who are time dependent.
  Duration get position => _audioPlayer.position;

  double get volume => _audioPlayer.volume;

  final Debouncer _debouncer = Debouncer();

  bool? _lastPlayingStateBeforeMinimise;

  final bool deleteTempDir;

  void setPosition(Duration value) {
    _audioPlayer.seek(value);
  }

  AudioPlayerBloc({this.deleteTempDir = true}) : super(AudioPlayerInitial()) {
    on<AudioPlayerLoadEvent>((event, _) async {
      try {
        //Stop the player if minimised.
        _listener ??= AppLifecycleListener(
          onHide: () {
            _lastPlayingStateBeforeMinimise = isPlaying;
            add(AudioPlayerPauseEvent());
          },
          onShow: () {
            if (_lastPlayingStateBeforeMinimise == true) {
              add(AudioPlayerPlayEvent());
            }
          },
        );

        final playingState = _audioPlayer.playing;
        final lastSeekPosition = _audioPlayer.position;
        if (_audioPlayer.playing) {
          await _audioPlayer.stop();
        }

        if (event.path.isNullOrEmpty) {
          audioDuration = Duration.zero;
          emitError('Empty URL');
          return;
        }

        switch (event.type) {
          case AudioType.url:
            await _audioPlayer.setUrl(event.path);
            break;
          case AudioType.file:
            await _audioPlayer.setFilePath(event.path);
            break;
        }

        audioDuration = _audioPlayer.duration ?? Duration.zero;

        // Seek to last position if same song
        if (lastSongId == event.songId) {
          await _audioPlayer.seek(lastSeekPosition);
        }
        lastSongId = event.songId;

        // Clear tracks if new annotation
        if (lastAnnotationId != event.annotationId) {
          metronomeTracks = [];
          downloadedBgmPath = null;
          downloadedGuidePath = null;
          tracks.clear();
        }
        lastAnnotationId = event.annotationId;

        emit(AudioPlayerLoaded());
        if ((lastLoadEvent == null && event.autoPlay) || playingState) {
          add(AudioPlayerPlayEvent());
        }
        lastLoadEvent = event;
      } catch (e) {
        logger.e(tag, error: e);
        //  emit(AudioPlayerError(error: e));
      }
    });

    on<AudioPlayerPlayEvent>((event, _) async {
      final bgmStartTime = DateTime.now();
      logger.d('$tag: BGM started playing at: $bgmStartTime');
      emit(AudioPlayerPlaying(startTime: bgmStartTime));
      unawaited(_audioPlayer.play());
    });

    on<AudioPlayerPauseEvent>((event, _) async {
      await _audioPlayer.pause();
      emit(AudioPlayerPaused());
    });

    on<AudioPlayerStopEvent>((event, _) async {
      await _audioPlayer.stop();
      await _audioPlayer.seek(Duration.zero);
      _lastPlayingStateBeforeMinimise = false;
      emit(AudioPlayerStopped());
    });

    on<AudioPlayerProgressUpdateEvent>((event, _) {
      emit(AudioPlayerProgressUpdated(position: event.position));
    });

    on<AudioPlayerSeekEvent>((event, _) async {
      _debouncer.debounce(() async {
        Duration seekPosition = Duration(milliseconds: event.milliseconds.toInt());
        await _audioPlayer.seek(seekPosition);
      }, 30);
    });

    on<AudioPlayerChangeVolumeEvent>((event, _) async {
      await _audioPlayer.setVolume(event.value);
    });

    _audioPlayer.playerStateStream.listen((state) async {
      if (state.processingState == ProcessingState.completed) {
        add(AudioPlayerStopEvent());
      }
    });

    // Start listening to the progress stream
    _audioPlayer.positionStream.listen((position) async {
      if (isClosed) return;
      add(AudioPlayerProgressUpdateEvent(position: position));
    });

    //Audio effects
    on<EnableMetronomeEvent>((event, _) async {
      try {
        await _downloadBgm(event.bgmS3Path, event.bgmVolume);
        tracks[MixerTrackType.bgm] = MixerTrack(
          id: MixerTrackType.bgm.createString(),
          path: downloadedBgmPath!,
          enabled: true,
          volume: event.bgmVolume,
        );

        metronomeTracks = mixer.createMetronomeTracks(
          tempo: event.tempo,
          timeSignature: event.timeSignature,
          volume: event.volume,
          hPath: await AssetPaths.extractAsset("assets/metronome_tone/met_h.wav"),
          lPath: await AssetPaths.extractAsset("assets/metronome_tone/met_l.wav"),
        );
        await exportAudio();
        unawaited(DI().resolve<AppToast>().showToast('Metronome enabled'));
      } catch (e) {
        emitError('Error creating metronome', error: e);
      }
    });

    on<DisableMetronomeEvent>((event, _) async {
      metronomeTracks = [];
      await exportAudio();
      unawaited(DI().resolve<AppToast>().showToast('Metronome disabled'));
    });

    on<EnableGuideTrackEvent>((event, _) async {
      try {
        await _downloadBgm(event.bgmS3Path, event.bgmVolume);
        if (downloadedGuidePath == null || downloadedGuidePath!.isEmpty) {
          final fileDownloader = FileDownloader();
          await fileDownloader.download(resource: event.guideS3Path, resourceType: FileResourceType.s3Url, type: FileType.guide);
          downloadedGuidePath = fileDownloader.downloadedFile!.path;
        }
        tracks[MixerTrackType.guide] = MixerTrack(
          id: MixerTrackType.guide.createString(),
          path: downloadedGuidePath!,
          volume: event.volume,
          enabled: true,
        );
        await exportAudio();
        unawaited(DI().resolve<AppToast>().showToast('Guide enabled'));
      } catch (e) {
        emitError('Error creating guide track', error: e);
      }
    });

    on<DisableGuideTrackEvent>((event, _) async {
      tracks.remove(MixerTrackType.guide);
      await exportAudio();
      unawaited(DI().resolve<AppToast>().showToast('Guide disabled'));
    });
  }

  @override
  Future<void> close() async {
    logger.d("$tag: closed");
    await _audioPlayer.dispose();
    _listener?.dispose();
    _debouncer.dispose();
    mixer.dispose();
    if (deleteTempDir && Platform.isIOS) {
      await FileDownloader.deleteAllTempDownload();
    }
    return super.close();
  }

  Future<void> _downloadBgm(String bgmUrl, double vol) async {
    if (downloadedBgmPath == null || downloadedBgmPath!.isEmpty) {
      final fileDownloader = FileDownloader();
      await fileDownloader.download(resource: bgmUrl, resourceType: FileResourceType.s3Url, type: FileType.bgm);
      downloadedBgmPath = fileDownloader.downloadedFile!.path;
    }
    // return downloadedBgmPath;
    tracks[MixerTrackType.bgm] = MixerTrack(
      id: MixerTrackType.bgm.createString(),
      path: downloadedBgmPath!,
      enabled: true,
      volume: vol,
    );
  }

  Future<void> exportAudio({bool? autoPlay}) async {
    try {
      tracks.forEach((trackType, model) {
        logger.i('$tag: Track ID: ${model.id}, Path: ${model.path}: vol:${model.volume}: ${model.duration}: ${model.fromTime}: ${model.offset}');
      });
      mixedAudioPath ??= '${await PathWrapper.getTempDownloadPath()}/mixed.wav';
      MixerComposeModel model = MixerComposeModel(
        tracks: tracks.values.toList()..addAll(metronomeTracks),
        output: mixedAudioPath!,
      );
      await mixer.export(model);

      add(AudioPlayerLoadEvent(
        annotationId: lastAnnotationId!,
        songId: lastSongId!,
        path: mixedAudioPath!,
        type: AudioType.file,
        autoPlay: autoPlay ?? lastLoadEvent?.autoPlay ?? false,
        source: lastLoadEvent?.source ?? AudioPlaySource.def,
      ));

      final recPath = await PathWrapper.getVocalRecordingPath();
      if (Platform.isIOS) {
        await record?.prepare(recPath: recPath, bgmPath: mixedAudioPath!);
      }
    } catch (e) {
      emitError('Error creating mix', error: e);
    }
  }

  void emitError(String message, {dynamic error}) {
    logger.e('$tag: $message', error: error);
    emit(AudioPlayerError(error: '$message \n ${error ?? ''}'));
    add(AudioPlayerStopEvent());
  }

  void setIfRecordingScreen(RecorderChannel? record) {
    this.record = record;
  }

  /// Runs [heavyOperation] after [delay] and emits [AudioPlayerInitial] if
  /// [heavyOperation] doesn't complete within [delay].
  ///
  /// [heavyOperation] is an operation that is expected to take more than
  /// [delay] milliseconds to complete. If [heavyOperation] completes
  /// successfully, [operationInProgress] is set to false and the loading timer
  /// is cancelled. If [heavyOperation] throws an error, [operationInProgress]
  /// is set to false and the error is rethrown.
  ///
  /// [delay] defaults to 100 milliseconds.
  ///
  /// This function is intended to be used for operations that are expected to
  /// take more than 100 milliseconds to complete. It prevents the UI from
  /// freezing while the operation is running.
  ///
  /// Note that if [heavyOperation] completes before [delay] milliseconds have
  /// passed, the loading timer is cancelled and [operationInProgress] is set
  /// to false.
  Future<void> withDelayedLoading(
    Future<void> Function() heavyOperation, {
    Duration delay = const Duration(milliseconds: 300),
  }) async {
    if (operationInProgress) {
      return;
    }
    operationInProgress = true;
    logger.d("$tag: Starting withDelayedLoading with delay: $delay");
    Timer? loadingTimer = Timer(delay, () {
      logger.d("$tag: Emitting AudioPlayerInitial due to delay");
      emit(AudioPlayerInitial());
    });

    try {
      await heavyOperation();
      if (loadingTimer.isActive) {
        loadingTimer.cancel();
      }
      logger.d("$tag: Heavy operation completed successfully");
      operationInProgress = false;
      return;
    } catch (e) {
      if (loadingTimer.isActive) {
        loadingTimer.cancel();
      }
      logger.e("$tag: Error during heavy operation", error: e);
      operationInProgress = false;
      rethrow;
    }
  }
}
