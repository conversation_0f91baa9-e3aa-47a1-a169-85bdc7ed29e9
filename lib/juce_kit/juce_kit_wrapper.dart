// ignore_for_file: public_member_api_docs, sort_constructors_first, prefer_function_declarations_over_variables
// ignore_for_file: non_constant_identifier_names, always_use_package_imports

import 'dart:async';
import 'dart:convert';
import 'dart:ffi';
import 'dart:io';
import "package:ffi/ffi.dart";
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:juce_mix_player/juce_mix_player.dart';
import 'juce_kit_gen.dart';

typedef NativeCallbackString = Void Function(Pointer<Utf8>);
typedef DartCallbackString = void Function(Pointer<Utf8>);

/// # Example
/// ```dart
/// Future<void> testJuceMixer() async {
///   const output = '/sdcard/Download/out.wav';
///   const vocal = '/sdcard/Download/vocal.mp3';
///   const music = '/sdcard/Download/A.wav';
///   const hPath = '/sdcard/Download/met_h.wav';
///   const lPath = '/sdcard/Download/met_l.wav';
///   const tempo = 115;
///   const timeSignature = "4/4";
///
///   JuceMixer mixer = JuceMixer();
///   double duration = await mixer.getAudioFileDuration(music);
///
///   MixerComposeModel model = MixerComposeModel(
///     tracks: [
///       MixerTrack(id: "vocal", path: vocal, enabled: false),
///       MixerTrack(id: "music", path: music),
///     ],
///     output: output,
///     outputDuration: duration,
///   );
///
///   model.tracks.addAll(
///     mixer.createMetronomeTracks(
///       tempo: tempo,
///       timeSignature: timeSignature,
///       volume: 1,
///       hPath: hPath,
///       lPath: lPath,
///     ),
///   );
///
///   try {
///       await mixer.export(model);
///   } catch (error) {
///       assert(true);
///   }
///
///   mixer.dispose();
///
///   assert(error.isEmpty);
/// ```
/// }
class JuceMixer {
  late final Pointer<Void> _ptr;
  late final JuceKitGen _lib;

  static String libname = "libjuce_jni.so";

  JuceMixer() {
    _lib = JuceKitGen(
        defaultTargetPlatform == TargetPlatform.iOS ? DynamicLibrary.process() : DynamicLibrary.open(libname));
    _ptr = _lib.JuceMixer_init();
  }

  void dispose() {
    _lib.JuceMixer_deinit(_ptr);
  }

  String getAudioFileDetails(String file) {
    return _lib.JuceMixer_getAudioFileDetails(_ptr, file.toNativeUtf8()).toDartString();
  }

  /**
   * ### Returns duration in seconds. If file not found then it reurns negetive value.
   */
  Future<double> getAudioFileDuration(String file) async {
    return _lib.JuceMixer_getAudioFileDuration(_ptr, file.toNativeUtf8());
  }

  Future<void> export(MixerComposeModel model) async {
    String error = _lib.JuceMixer_exportToFile(_ptr, jsonEncode(model.toJson()).toNativeUtf8()).toDartString();
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }

  List<MixerTrack> createMetronomeTracks({
    required int tempo,
    required String timeSignature,
    required double volume,
    required String hPath,
    required String lPath,
  }) {
    int beatCount;
    List<MixerTrack> list = [];
    double tempo_ = tempo.toDouble();

    switch (timeSignature) {
      case "3_by_4":
        beatCount = 3;
        break;
      case "6_by_8":
        beatCount = 3;
        tempo_ *= 2; // Double the tempo for 6/8
        break;
      case "4_by_4":
        beatCount = 4;
        break;
      case "12_by_8":
        beatCount = 4;
        tempo_ *= 2; // Double the tempo for 12/8
        break;
      case "5_by_8":
        beatCount = 5;
        break;
      case "7_by_8":
        beatCount = 7;
        break;
      default:
        beatCount = 1; // For unsupported time signatures
        break;
    }

    for (var i = 0; i < beatCount; i++) {
      double interval = 60.0 / tempo_;
      list.add(
        MixerTrack(
          id: "metronome_track_$i",
          path: i == 0 && beatCount > 1 ? hPath : lPath,
          volume: volume,
          offset: i * interval,
          repeat: true,
          repeatInterval: interval * beatCount,
        ),
      );
    }

    return list;
  }
}

class ProcessingGraphFileCreatedEvent {
  int callerId;
  String file;

  ProcessingGraphFileCreatedEvent({
    required this.callerId,
    required this.file,
  });
}

class ProcessingGraphMasterFileCreatedEvent {
  int callerId;
  String file;

  ProcessingGraphMasterFileCreatedEvent({
    required this.callerId,
    required this.file,
  });
}

class JuceKitWrapper {
  late final JuceKitGen _lib;
  static String libname = "libjuce_jni.so";
  static JuceKitWrapper shared = JuceKitWrapper();

  JuceKitWrapper() {
    _lib = JuceKitGen(
        defaultTargetPlatform == TargetPlatform.iOS ? DynamicLibrary.process() : DynamicLibrary.open(libname));
  }

  void enableLogs(bool enabled) {
    _lib.juce_enableLogs(enabled ? 1 : 0);
  }

  Future<String> getAudioFileDetails(String input) async {
    return _lib.juce_getAudioFileDetails(input.toNativeUtf8()).toDartString();
  }

  Future<void> convertWavToFlac(String inPath, String outPath) async {
    String error = _lib.juce_convertWavToFlac(inPath.toNativeUtf8(), outPath.toNativeUtf8()).toDartString();
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }

  /// Applies a processing graph to an audio file and outputs the result.
  ///
  /// This method processes an audio file using a specified configuration and
  /// genre mapping, and outputs the processed file to a specified directory.
  ///
  /// Example usage:
  /// ```dart
  /// Future<void> testProcessingGraph() async {
  ///   const vocal = '/sdcard/Download/vocal.mp3';
  ///   const config = '/sdcard/Download/config.json';
  ///   const outputDir = '/sdcard/Download/out/';
  ///   const genreMap = '/sdcard/Download/genre_map.json';
  ///   const bgmGenre = 'Unplugged';
  ///
  ///   var juceKit = JuceKitWrapper.shared;
  ///
  ///   print("applyProcessingGraph start");
  ///   await juceKit.applyProcessingGraph(vocal, outputDir, config, genreMap, bgmGenre, (file) {
  ///     print("complete: $file");
  ///   });
  ///   print('applyProcessingGraph outCount: ${Directory(outputDir).listSync()}');
  /// }
  /// ```
  ///
  /// Throws an [Exception] if an error occurs during processing.
  ///
  /// Parameters:
  /// - `callerId`: An identifier for the caller.
  /// - `input`: The path to the input audio file.
  /// - `output`: The directory where the processed audio file will be saved.
  /// - `config`: The path to the configuration file for processing.
  /// - `genreMapPath`: The path to the genre mapping file.
  /// - `bgmGenre`: The background music genre to be applied.
  Future<void> applyProcessingGraph(
    String input,
    String output,
    String config,
    String genreMapPath,
    String bgmGenre,
    void Function(String) callback,
  ) async {
    final completer = Completer<String>();

    DartCallbackString callback_ = (error) {
      callback(error.toDartString());
    };
    var callback__ = NativeCallable<NativeCallbackString>.listener(callback_);

    DartCallbackString completion_ = (error) {
      completer.complete(error.toDartString());
    };
    var completion__ = NativeCallable<NativeCallbackString>.listener(completion_);
    _lib
        .juce_applyProcessingGraph(
          input.toNativeUtf8(),
          output.toNativeUtf8(),
          config.toNativeUtf8(),
          genreMapPath.toNativeUtf8(),
          bgmGenre.toNativeUtf8(),
          callback__.nativeFunction,
          completion__.nativeFunction,
        )
        .toDartString();
    String error = await completer.future;
    completion__.close();
    callback__.close();
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }

  /// Applies a master processing graph to an audio file and outputs the result.
  ///
  /// This method processes an audio file using a specified master configuration
  /// and genre mapping, and outputs the processed file to a specified directory.
  ///
  /// Example usage:
  /// ```dart
  /// Future<void> testProcessingGraphMaster() async {
  ///   const vocal = '/sdcard/Download/vocal.mp3';
  ///   const config = '/sdcard/Download/config_master.json';
  ///   const outputDir = '/sdcard/Download/out_master/';
  ///   const genreMap = '/sdcard/Download/genre_map.json';
  ///   const bgmGenre = 'Unplugged';
  ///
  ///   var juceKit = JuceKitWrapper.shared;
  ///
  ///   print("applyProcessingGraph start");
  ///   await juceKit.applyProcessingGraphMaster(vocal, outputDir, config, genreMap, bgmGenre, (file) {
  ///     print("complete: $file");
  ///   });
  ///   print('applyProcessingGraph outCount: ${Directory(outputDir).listSync()}');
  /// }
  /// ```
  ///
  /// Throws an [Exception] if an error occurs during processing.
  ///
  /// Parameters:
  /// - `input`: The path to the input audio file.
  /// - `output`: The directory where the processed audio file will be saved.
  /// - `config`: The path to the master configuration file for processing.
  /// - `genreMapPath`: The path to the genre mapping file.
  /// - `bgmGenre`: The background music genre to be applied.
  Future<void> applyProcessingGraphMaster(
    String input,
    String output,
    String config,
    String genreMapPath,
    String bgmGenre,
    void Function(String) callback,
  ) async {
    final completer = Completer<String>();

    DartCallbackString callback_ = (error) {
      callback(error.toDartString());
    };
    var callback__ = NativeCallable<NativeCallbackString>.listener(callback_);

    DartCallbackString completion_ = (error) {
      completer.complete(error.toDartString());
    };
    var completion__ = NativeCallable<NativeCallbackString>.listener(completion_);
    _lib
        .juce_applyProcessingGraphMaster(
          input.toNativeUtf8(),
          output.toNativeUtf8(),
          config.toNativeUtf8(),
          genreMapPath.toNativeUtf8(),
          bgmGenre.toNativeUtf8(),
          callback__.nativeFunction,
          completion__.nativeFunction,
        )
        .toDartString();
    String error = await completer.future;
    completion__.close();
    callback__.close();
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }

  Future<void> reverb(String input, String output, String config) async {
    String error =
        _lib.juce_applyReverbEffect(input.toNativeUtf8(), output.toNativeUtf8(), config.toNativeUtf8()).toDartString();
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }

  void initJuceMixPlayerAudioFilters(String vocalConfigPath, String masterConfigPath) async {
    String vocal = await File(vocalConfigPath).readAsString();
    String master = await File(masterConfigPath).readAsString();
    _lib.juce_initAudioFilters(vocal.toNativeUtf8(), master.toNativeUtf8());
  }

  Future<void> applyStereoAndNormalizeVocal(String input, String output) async {
    final completer = Completer<String>();

    DartCallbackString completion_ = (error) {
      completer.complete(error.toDartString());
    };

    var completion__ = NativeCallable<NativeCallbackString>.listener(completion_);

    _lib.juce_applyStereoAndNormalizeVocal(
      input.toNativeUtf8(),
      output.toNativeUtf8(),
      completion__.nativeFunction,
    );

    String error = await completer.future;
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }
}

class JuceOfflineMixModel extends Equatable {
  final String vocal_file_path;
  final int vocal_delay_ms;
  final double vocal_volume;
  final String music_file_path;
  final int music_delay_ms;
  final double music_volume;
  final String mix_file_path;

  const JuceOfflineMixModel({
    required this.vocal_file_path,
    required this.vocal_delay_ms,
    required this.vocal_volume,
    required this.music_file_path,
    required this.music_delay_ms,
    required this.music_volume,
    required this.mix_file_path,
  });

  Map<String, dynamic> toJson() {
    return {
      "vocal_file_path": vocal_file_path,
      "vocal_delay_ms": vocal_delay_ms,
      "vocal_volume": vocal_volume,
      "music_file_path": music_file_path,
      "music_delay_ms": music_delay_ms,
      "music_volume": music_volume,
      "mix_file_path": mix_file_path,
      "block_size": 1000
    };
  }

  JuceOfflineMixModel copyWith({
    String? vocal_file_path,
    int? vocal_delay_ms,
    double? vocal_volume,
    String? music_file_path,
    int? music_delay_ms,
    double? music_volume,
    String? mix_file_path,
  }) {
    return JuceOfflineMixModel(
      vocal_file_path: vocal_file_path ?? this.vocal_file_path,
      vocal_delay_ms: vocal_delay_ms ?? this.vocal_delay_ms,
      vocal_volume: vocal_volume ?? this.vocal_volume,
      music_file_path: music_file_path ?? this.music_file_path,
      music_delay_ms: music_delay_ms ?? this.music_delay_ms,
      music_volume: music_volume ?? this.music_volume,
      mix_file_path: mix_file_path ?? this.mix_file_path,
    );
  }

  @override
  List<Object?> get props => [
        vocal_file_path,
        vocal_delay_ms,
        vocal_volume,
        music_file_path,
        music_delay_ms,
        music_volume,
        mix_file_path,
      ];
}

extension JuceMixPlayerExtension on JuceMixPlayer {

  void setVocalFilter(String pass) {
    String error =
        JuceKitWrapper.shared._lib.JuceMixPlayer_setVocalFilter(this.getPtr(), pass.toNativeUtf8()).toDartString();
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }

  void setMasterFilter(String pass) {
    String error =
        JuceKitWrapper.shared._lib.JuceMixPlayer_setMasterFilter(this.getPtr(), pass.toNativeUtf8()).toDartString();
    if (error.isNotEmpty) {
      throw Exception(error);
    }
  }
}
