import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:melodyze/core/core_modules/file_downloader/file_downloader.dart';
import 'package:melodyze/core/generic_bloc/events.dart';
import 'package:melodyze/core/generic_bloc/safe_bloc.dart';
import 'package:melodyze/core/generic_bloc/states.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/modules/song_personalization/models/annotated_data/annotated_data.dart';

part 'audio_download_event.dart';
part 'audio_download_state.dart';

enum AudioFileType { bgm, guide }

class AudioDownloadResult {
  final String? bgmPath;
  final String? guidePath;
  final bool isBgmDownloaded;
  final bool isGuideDownloaded;

  const AudioDownloadResult({
    this.bgmPath,
    this.guidePath,
    required this.isBgmDownloaded,
    required this.isGuideDownloaded,
  });

  bool get isAllDownloaded => isBgmDownloaded && (guidePath != null ? isGuideDownloaded : true);

  AudioDownloadResult copyWith({
    String? bgmPath,
    String? guidePath,
    bool? isBgmDownloaded,
    bool? isGuideDownloaded,
  }) {
    return AudioDownloadResult(
      bgmPath: bgmPath ?? this.bgmPath,
      guidePath: guidePath ?? this.guidePath,
      isBgmDownloaded: isBgmDownloaded ?? this.isBgmDownloaded,
      isGuideDownloaded: isGuideDownloaded ?? this.isGuideDownloaded,
    );
  }
}

class AudioDownloadManager extends SafeBloc<AudioDownloadEvent, AudioDownloadState> {
  static const tag = "AudioDownloadManager";

  // Current download result
  AudioDownloadResult _currentResult = const AudioDownloadResult(
    isBgmDownloaded: false,
    isGuideDownloaded: false,
  );

  // Current annotation data
  AnnotatedData? _currentAnnotatedData;

  // Download instances
  FileDownloader? _bgmDownloader;
  FileDownloader? _guideDownloader;

  AudioDownloadManager() : super(AudioDownloadInitial()) {
    on<StartAudioDownloadEvent>(_onStartAudioDownload);
    on<CheckDownloadStatusEvent>(_onCheckDownloadStatus);
    on<RetryDownloadEvent>(_onRetryDownload);
    on<ClearDownloadEvent>(_onClearDownload);
  }

  // Getters
  AudioDownloadResult get currentResult => _currentResult;
  AnnotatedData? get currentAnnotatedData => _currentAnnotatedData;
  bool get isBgmDownloaded => _currentResult.isBgmDownloaded;
  bool get isGuideDownloaded => _currentResult.isGuideDownloaded;
  bool get isAllDownloaded => _currentResult.isAllDownloaded;
  String? get bgmPath => _currentResult.bgmPath;
  String? get guidePath => _currentResult.guidePath;

  // Helper methods for UI components
  bool get isBgmDownloading => state is AudioDownloadInProgress && (state as AudioDownloadInProgress).bgmDownloading;
  bool get isGuideDownloading => state is AudioDownloadInProgress && (state as AudioDownloadInProgress).guideDownloading;
  bool get isDownloading => state is AudioDownloadInProgress;
  bool get hasError => state is AudioDownloadError;

  Future<void> _onStartAudioDownload(StartAudioDownloadEvent event, Emitter<AudioDownloadState> emit) async {
    try {
      _currentAnnotatedData = event.annotatedData;

      // Reset current result
      _currentResult = const AudioDownloadResult(
        isBgmDownloaded: false,
        isGuideDownloaded: false,
      );

      emit(AudioDownloadInProgress(
        bgmDownloading: true,
        guideDownloading: event.annotatedData.guideVocalPath?.isNotEmpty ?? false,
        result: _currentResult,
      ));

      // Create downloaders
      _bgmDownloader = FileDownloader();
      _guideDownloader = FileDownloader();

      // Start downloads
      final List<Future<void>> downloadFutures = [
        _downloadBgm(event.annotatedData.songPath),
      ];

      // Add guide download if available
      if (event.annotatedData.guideVocalPath?.isNotEmpty ?? false) {
        downloadFutures.add(_downloadGuide(event.annotatedData.guideVocalPath!));
      } else {
        // If no guide track, mark as downloaded
        _currentResult = _currentResult.copyWith(isGuideDownloaded: true);
      }

      // Wait for all downloads to complete
      await Future.wait(downloadFutures);

      // Update final result
      _currentResult = _currentResult.copyWith(
        bgmPath: _bgmDownloader?.downloadedFile?.path,
        guidePath: _guideDownloader?.downloadedFile?.path,
      );

      emit(AudioDownloadCompleted(result: _currentResult));
    } catch (e, s) {
      logger.e(tag, error: e, stackTrace: s);
      emit(AudioDownloadError(error: e.toString()));
    }
  }

  Future<void> _downloadBgm(String bgmUrl) async {
    try {
      await _bgmDownloader!.download(
        resource: bgmUrl,
        resourceType: FileResourceType.s3Url,
        type: FileType.bgm,
      );

      _currentResult = _currentResult.copyWith(
        isBgmDownloaded: true,
        bgmPath: _bgmDownloader!.downloadedFile?.path,
      );

      logger.d("$tag: BGM download completed: ${_currentResult.bgmPath}");

      // Emit BGM download completion state
      if (!isClosed) {
        emit(BgmDownloadCompleted(result: _currentResult));
      }
    } catch (e) {
      logger.e("$tag: Error downloading BGM", error: e);
      rethrow;
    }
  }

  Future<void> _downloadGuide(String guideUrl) async {
    try {
      await _guideDownloader!.download(
        resource: guideUrl,
        resourceType: FileResourceType.s3Url,
        type: FileType.guide,
      );

      _currentResult = _currentResult.copyWith(
        isGuideDownloaded: true,
        guidePath: _guideDownloader!.downloadedFile?.path,
      );

      logger.d("$tag: Guide download completed: ${_currentResult.guidePath}");

      // Emit guide download completion state
      if (!isClosed) {
        emit(GuideDownloadCompleted(result: _currentResult));
      }
    } catch (e) {
      logger.e("$tag: Error downloading Guide", error: e);
      rethrow;
    }
  }

  Future<void> _onCheckDownloadStatus(CheckDownloadStatusEvent event, Emitter<AudioDownloadState> emit) async {
    // Emit progress update
    emit(AudioDownloadInProgress(
      bgmDownloading: !_currentResult.isBgmDownloaded,
      guideDownloading: !_currentResult.isGuideDownloaded && (_currentAnnotatedData?.guideVocalPath?.isNotEmpty ?? false),
      result: _currentResult,
    ));
  }

  Future<void> _onRetryDownload(RetryDownloadEvent event, Emitter<AudioDownloadState> emit) async {
    if (_currentAnnotatedData != null) {
      logger.i("$tag: Retrying download for annotation: ${_currentAnnotatedData!.id}");
      add(StartAudioDownloadEvent(annotatedData: _currentAnnotatedData!));
    } else {
      logger.e("$tag: Cannot retry download - no current annotation data");
      emit(AudioDownloadError(error: "No annotation data available for retry"));
    }
  }

  Future<void> _onClearDownload(ClearDownloadEvent event, Emitter<AudioDownloadState> emit) async {
    _currentResult = const AudioDownloadResult(
      isBgmDownloaded: false,
      isGuideDownloaded: false,
    );
    _currentAnnotatedData = null;
    _bgmDownloader = null;
    _guideDownloader = null;

    emit(AudioDownloadInitial());
  }

  @override
  Future<void> close() {
    _bgmDownloader = null;
    _guideDownloader = null;
    return super.close();
  }
}
