part of 'audio_download_manager.dart';

abstract class AudioDownloadEvent extends BaseEvent {
  const AudioDownloadEvent();
}

class StartAudioDownloadEvent extends AudioDownloadEvent {
  final AnnotatedData annotatedData;
  
  const StartAudioDownloadEvent({required this.annotatedData});
  
  @override
  List<Object?> get props => [annotatedData];
}

class CheckDownloadStatusEvent extends AudioDownloadEvent {
  const CheckDownloadStatusEvent();
  
  @override
  List<Object?> get props => [];
}

class RetryDownloadEvent extends AudioDownloadEvent {
  const RetryDownloadEvent();
  
  @override
  List<Object?> get props => [];
}

class ClearDownloadEvent extends AudioDownloadEvent {
  const ClearDownloadEvent();
  
  @override
  List<Object?> get props => [];
}

class CheckBgmAvailabilityEvent extends AudioDownloadEvent {
  const CheckBgmAvailabilityEvent();
  
  @override
  List<Object?> get props => [];
}

class CheckGuideAvailabilityEvent extends AudioDownloadEvent {
  const CheckGuideAvailabilityEvent();
  
  @override
  List<Object?> get props => [];
}
