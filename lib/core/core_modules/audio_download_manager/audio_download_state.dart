part of 'audio_download_manager.dart';

abstract class AudioDownloadState extends BlocState {
  const AudioDownloadState();
}

class AudioDownloadInitial extends AudioDownloadState {
  @override
  List<Object?> get props => [];
}

class AudioDownloadInProgress extends AudioDownloadState {
  final bool bgmDownloading;
  final bool guideDownloading;
  final AudioDownloadResult result;
  
  const AudioDownloadInProgress({
    required this.bgmDownloading,
    required this.guideDownloading,
    required this.result,
  });
  
  @override
  List<Object?> get props => [bgmDownloading, guideDownloading, result];
}

class AudioDownloadCompleted extends AudioDownloadState {
  final AudioDownloadResult result;
  
  const AudioDownloadCompleted({required this.result});
  
  @override
  List<Object?> get props => [result];
}

class AudioDownloadError extends AudioDownloadState {
  final String error;
  
  const AudioDownloadError({required this.error});
  
  @override
  List<Object?> get props => [error];
}

class BgmDownloadCompleted extends AudioDownloadState {
  final AudioDownloadResult result;
  
  const BgmDownloadCompleted({required this.result});
  
  @override
  List<Object?> get props => [result];
}

class GuideDownloadCompleted extends AudioDownloadState {
  final AudioDownloadResult result;
  
  const GuideDownloadCompleted({required this.result});
  
  @override
  List<Object?> get props => [result];
}
