import 'dart:io';

import 'package:melodyze/core/services/aws_client.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';

enum FileResourceType { url, s3Url }

enum FileType {
  images,
  bgm,
  guide,
  others
}

class FileDownloader {
  String? s3FilePath;
  String? donwloadDir;
  File? downloadedFile;
  FileDownloader();

  /// Downloads a file from the specified S3 path.
  ///
  /// If the file is already downloaded and exists locally, the download is skipped.
  ///
  /// If the `s3FilePath` is empty, the function logs a message and returns.
  ///
  /// The downloaded file is stored in the specified `downloadDir` or in a temporary path if
  /// `downloadDir` is not provided.
  ///
  /// Logs the file path upon successful download or logs an error if the download fails.
  ///
  /// Args:
  ///   s3FilePath (String): The path of the file in S3 to download. Must not be empty.
  ///   donwloadDir (String?, optional): The directory where the file should be downloaded. Defaults to a temporary path.
  ///
  /// Returns:
  ///   Future void: A Future that completes when the download is finished.
  Future<void> download({required String resource, required FileResourceType resourceType, required FileType type}) async {
    try {
      if (resource.isEmpty) {
        logger.d("Resource path is empty");
        return;
      }

      late dynamic response;
      if (resourceType == FileResourceType.s3Url) {
        response = await DI().resolve<AwsClient>().downloadFileFromSignedUrl(resource, type);
      }

      if (response == null) {
        logger.d("File not found");
        return;
      }
      downloadedFile = File(response);
      logger.d("File downloaded: ${downloadedFile!.path}");
    } catch (e) {
      logger.e("Error downloading file", error: e);
    }
  }

  /// Deletes the downloaded file if it exists.
  ///
  /// Logs an error if the deletion fails.
  Future<void> delete() async {
    try {
      await downloadedFile?.delete();
      downloadedFile = null;
      logger.d("File deleted");
    } catch (e) {
      logger.e("Error deleting file", error: e);
    }
  }

  /// Delete all files in the donwloadDir locally.
  /// If donwloadDir is null, does nothing.
  /// Logs an error if the deletion fails.
  Future<void> deleteAll() async {
    try {
      //Delete all files in the donwloadDir locally
      if (donwloadDir != null) {
        await Directory(donwloadDir!).delete(recursive: true);
      }

      logger.d("All files deleted in directory: $donwloadDir");
    } catch (e) {
      logger.e("Error deleting all files", error: e);
    }
  }

  /// Deletes all files in the temporary download directory.
  ///
  /// This function does not require an instance of [FileDownloader] to be called.
  /// It is useful when you want to delete all temporary files without having to
  /// create an instance of [FileDownloader].
  ///
  /// Logs an error if the deletion fails.
  static Future<void> deleteAllTempDownload() async {
    try {
      String? donwloadDir = await PathWrapper.getTempDownloadPath();
      final dir = Directory(donwloadDir);
      if (dir.existsSync()) {
        await dir.delete(recursive: true);
        logger.d("All files deleted in directory: $donwloadDir");
      } else {
        logger.d("No files to delete in directory: $donwloadDir");
      }
    } catch (e) {
      logger.e("Error deleting all files", error: e);
    }
  }
}
