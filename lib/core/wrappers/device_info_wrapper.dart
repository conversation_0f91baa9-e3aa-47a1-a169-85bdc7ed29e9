import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:uuid/uuid.dart';

class UserDeviceInfo {
  final String platfrom;
  final String device;
  final String os;
  final String uid;

  UserDeviceInfo({required this.platfrom, required this.device, required this.os, required this.uid});
}

class DeviceInfoWrapper {
  DeviceInfoWrapper._();

  static Future<UserDeviceInfo> getSystemInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    Uuid uuid = Uuid();

    final String baseKey = "ai.melodyze.music.fcmkey";

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;

      final deviceId = androidInfo.id;
      final deviceFingerprint = androidInfo.fingerprint;
      final deviceModel = androidInfo.model;
      final deviceHost = androidInfo.host;
      final isPhysicalDevice = androidInfo.isPhysicalDevice;
      final namespaceUrl = UuidValue.fromNamespace(Namespace.url).toString();
      final uid = uuid.v5(namespaceUrl, '$deviceId$deviceFingerprint$deviceModel$deviceHost$isPhysicalDevice$baseKey');

      return UserDeviceInfo(
        platfrom: "android",
        device: '${androidInfo.manufacturer} ${androidInfo.model}',
        os: 'Android ${androidInfo.version.release}',
        uid: uid,
      );
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;

      SecureStorageHelper secureStorage = SecureStorageHelper();
      String currentUid = await secureStorage.read(baseKey) ?? '';
      if (currentUid.isEmpty) {
        final generatedId = uuid.v4();
        await secureStorage.write(baseKey, generatedId);
        currentUid = generatedId;
      }

      return UserDeviceInfo(
          platfrom: "ios",
          device: '${iosInfo.name} ${iosInfo.model}',
          os: '${iosInfo.systemName} ${iosInfo.systemVersion}',
          uid: currentUid);
    }
    return UserDeviceInfo(platfrom: "", device: "", os: "", uid: "");
  }
}
