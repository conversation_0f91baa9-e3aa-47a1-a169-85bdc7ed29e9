import 'package:fluttertoast/fluttertoast.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';

class AppToast {
  Future<bool?>? showToast(String message) {
    try {
      return Fluttertoast.showToast(
          msg: message,
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          timeInSecForIosWeb: 1,
          backgroundColor: AppColors.darkCyan,
          textColor: AppColors.white70);
    } catch (e) {
      logger.e(e.toString());
    }
    return null;
  }
}
