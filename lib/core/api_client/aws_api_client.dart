import 'package:dio/dio.dart';

class AwsApiClient {
  static final Dio _dio = Dio();

  static Future<Response<dynamic>> get(String url, String contentType) async {
    try {
      return await _dio.get(Uri.parse(url).toString(), options: Options(headers: {'Content-Type': contentType}));
    } catch (e) {
      throw _throwException(e);
    }
  }

  static Future<Response<dynamic>> post(String url, dynamic body, String contentType) async {
    try {
      return await _dio.post(Uri.parse(url).toString(), data: body, options: Options(headers: {'Content-Type': contentType}));
    } catch (e) {
      throw _throwException(e);
    }
  }

  static Future<Response<dynamic>> put(String url, dynamic body, String contentType) async {
    try {
      return await _dio.put(Uri.parse(url).toString(), data: body, options: Options(headers: {'Content-Type': contentType}));
    } catch (e) {
      throw _throwException(e);
    }
  }

  static Future<Response<dynamic>> delete(String url, dynamic body, String contentType) async {
    try {
      return await _dio.delete(Uri.parse(url).toString(), data: body, options: Options(headers: {'Content-Type': contentType}));
    } catch (e) {
      throw _throwException(e);
    }
  }

  static Exception _throwException(Object error) {
    if (error is DioException) {
      throw Exception(error.message);
    }
    throw Exception();
  }
}