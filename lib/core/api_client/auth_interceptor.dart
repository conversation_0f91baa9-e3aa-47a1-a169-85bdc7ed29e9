import 'package:dio/dio.dart';
import 'package:melodyze/core/wrappers/event_bus.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/secure_storage_helper.dart';
import 'package:melodyze/modules/auth/bloc/authentication_event.dart';

class AuthInterceptor extends Interceptor {
  @override
  Future<void> onRequest(
      RequestOptions options, RequestInterceptorHandler handler) async {
    options.headers['accept'] = options.headers['accept'] ?? 'application/json';
    options.headers['Content-Type'] =
        options.headers['Content-Type'] ?? 'application/json';
    options.headers['x-auth-token'] =
        await DI().resolve<AccessTokenHelper>().accessToken;
    handler.next(options);
  }

  @override
  Future<void> onResponse(
      Response response, ResponseInterceptorHandler handler) async {
    final String? authToken = response.headers['x-access-token']?.first;
    if (authToken != null) {
      await DI().resolve<AccessTokenHelper>().setAccessToken(authToken);
    }
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 401) {
      DI().resolve<EventBus>().fire(SignOut());
    }
    return super.onError(err, handler);
  }
}
