import 'dart:convert';
import 'package:dio/dio.dart';

class LocationService {
  static const _url = 'http://ip-api.com/json?fields=2146754';

  /// Fetches the continent code from ip-api.com
  static Future<String?> getContinentCode() async {
    try {
      final dio = Dio();
      final response = await dio.get(_url);
      if (response.statusCode == 200) {
        final data = response.data is String ? json.decode(response.data) : response.data;
        return data['continentCode'] as String?;
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
