import 'dart:io';

// ignore: depend_on_referenced_packages
import 'package:melodyze/core/api_client/api_client.dart';
import 'package:melodyze/core/api_client/aws_api_client.dart';
import 'package:melodyze/core/core_modules/file_downloader/file_downloader.dart';
import 'package:melodyze/core/utilities/utils/utils.dart';
import 'package:melodyze/core/wrappers/app_logger.dart';
import 'package:melodyze/core/wrappers/injector.dart';
import 'package:melodyze/core/wrappers/path_wrapper.dart';


class AwsClient {
  // Future<Result> uploadFile({
  //   required String bucketPath,
  //   required String filePath,
  //   String? fileName,
  //   String? userId,
  // }) async {
  //   try {
  //     const region = AwsConfig.awsRegion;

  //     final fileNameAndExtension = FileUtils.extractFileExtension(filePath);
  //     final tempFileName = fileName ?? fileNameAndExtension.fileName;
  //     final timestamp = DateTime.now().millisecondsSinceEpoch;
  //     final randomPart = StringUtils.generateRandomString(6);
  //     String extension = fileNameAndExtension.extension;
  //     //Append userId as foldername if it is not null and timestamp with random part with filename
  //     final newFileName = '${userId != null ? '$userId/' : ''}${tempFileName}_${Platform.operatingSystem}_${randomPart}_$timestamp$extension';

  //     // Create a signer which uses the `default` profile from the shared
  //     // credentials file.
  //     const signer = AWSSigV4Signer(
  //       credentialsProvider: AWSCredentialsProvider.environment(),
  //     );

  //     // Set up S3 values
  //     final scope = AWSCredentialScope(
  //       region: region,
  //       service: AWSService.s3,
  //     );
  //     const host = 's3.$region.amazonaws.com';
  //     final serviceConfiguration = S3ServiceConfiguration();

  //     // Create the bucket
  //     // final createBody = utf8.encode(
  //     //   '''
  //     //   <CreateBucketConfiguration xmlns="http://s3.amazonaws.com/doc/2006-03-01/">
  //     //   <LocationConstraint>$region</LocationConstraint>
  //     //   </CreateBucketConfiguration>
  //     //   ''',
  //     // );
  //     // final createRequest = AWSHttpRequest.put(
  //     //   Uri.https(host, bucketPath),
  //     //   body: createBody,
  //     //   headers: {
  //     //     AWSHeaders.host: host,
  //     //     AWSHeaders.contentLength: createBody.length.toString(),
  //     //     AWSHeaders.contentSHA256: await FileUtils.calculateFileSha256(filePath),
  //     //     AWSHeaders.contentType: 'application/xml',
  //     //   },
  //     // );

  //     // print('Creating bucket $bucketPath...');
  //     // final signedCreateRequest = await signer.sign(
  //     //   createRequest,
  //     //   credentialScope: scope,
  //     //   serviceConfiguration: serviceConfiguration,
  //     // );
  //     // final createResponse = await signedCreateRequest.send().response;
  //     // final createStatus = createResponse.statusCode;
  //     // print('Create Bucket Response: $createStatus');
  //     // if (createStatus == 409) {
  //     //     exitWithError('Bucket name already exists!', createStatus);
  //     // }
  //     // if (createStatus != 200) {
  //     //     exitWithError('Bucket creation failed', createStatus);
  //     // }
  //     // print('Bucket creation succeeded!');

  //     // Upload the file
  //     final file = File(filePath).openRead();
  //     final path = '$bucketPath/$newFileName';
  //     final uploadRequest = AWSStreamedHttpRequest.put(
  //       Uri.https(host, path),
  //       body: file,
  //       headers: {
  //         AWSHeaders.host: host,
  //         AWSHeaders.contentSHA256: await FileUtils.calculateFileSha256(filePath),
  //         AWSHeaders.contentType: FileUtils.getContentTypeFromFilePath(filePath),
  //       },
  //     );

  //     final signedUploadRequest = await signer.sign(
  //       uploadRequest,
  //       credentialScope: scope,
  //       serviceConfiguration: serviceConfiguration,
  //     );
  //     final uploadResponse = await signedUploadRequest.send().response;
  //     final uploadStatus = uploadResponse.statusCode;
  //     if (uploadStatus != 200) {
  //       throwError('Could not upload file', uploadStatus, filePath);
  //     }
  //     return Success(path);
  //   } catch (error) {
  //     return Error(error.toString());
  //   }
  // }

  // void throwError(String error, [int? statusCode, String? filePath]) {
  //   throw AwsException(error: error, statusCode: statusCode, filePath: filePath);
  // }

  // String resolveS3Path(String path) {
  //   String newPath = path.startsWith('s3:/') ? path.replaceAll('s3:/', '') : path;
  //   if (!newPath.startsWith('/')) newPath = '/$newPath';
  //   return newPath;
  // }

  // Future<String> getSignedUrl(String path) async {
  //   path = resolveS3Path(path);
  //   const region = AwsConfig.awsRegion;
  //   // Create a signer which uses the environment --dart define.
  //   const signer = AWSSigV4Signer(
  //     credentialsProvider: AWSCredentialsProvider.environment(),
  //   );

  //   // Set up S3 values
  //   final scope = AWSCredentialScope(
  //     region: region,
  //     service: AWSService.s3,
  //   );
  //   const host = 's3.$region.amazonaws.com';
  //   final serviceConfiguration = S3ServiceConfiguration();

  //   // Create a pre-signed URL for downloading the file
  //   final urlRequest = AWSHttpRequest.get(
  //     Uri.https(host, path),
  //     headers: const {
  //       AWSHeaders.host: host,
  //     },
  //   );
  //   final signedUrl = await signer.presign(
  //     urlRequest,
  //     credentialScope: scope,
  //     serviceConfiguration: serviceConfiguration,
  //     expiresIn: const Duration(minutes: 10),
  //   );
  //   return signedUrl.toString();
  // }

  // Future<String> downloadFileThrows(String path, {String? downloadDir}) async {
  //   path = resolveS3Path(path);
  //   final documentsDirpath = downloadDir ?? (await getApplicationDocumentsDirectory()).path;
  //   final outputPath = '$documentsDirpath/downloaded_audios$path';
  //   if (File(outputPath).existsSync()) {
  //     return outputPath;
  //   }
  //   final signedUrl = await DI().resolve<AwsClient>().getSignedUrl(path);
  //   final filePath = await DI().resolve<ApiClient>().download(signedUrl, outputPath);
  //   if (filePath != null && File(filePath).existsSync()) {
  //     return filePath;
  //   }
  //   throw const Error('Unknown error');
  // }

  // Future<String?> downloadFile(String path, {String? downloadDir}) async {
  //   try {
  //     return downloadFileThrows(path, downloadDir: downloadDir);
  //   } catch (e) {
  //     logger.e(e);
  //     return null;
  //   }
  // }

  Future<String> downloadFileFromSignedUrl(String signedUrl, FileType type) async {
    Uri uri = Uri.parse(signedUrl);
    final tempDir = '${await PathWrapper.getTempDownloadPath()}/${type.name}';
    String outputPath = '$tempDir/${uri.pathSegments.join("_")}';
    if (File(outputPath).existsSync()) {
      logger.d("S3 File already downloaded: $outputPath");
      return outputPath;
    }
    final filePath = await DI().resolve<ApiClient>().download(signedUrl, outputPath);
    if (filePath != null && File(filePath).existsSync()) {
      return filePath;
    }
    throw const Error('Unknown error');
  }

  String getS3FilenameFromPath(String filePath) {
    final fileNameAndExtension = FileUtils.extractFileExtension(filePath);
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    String randomPart = StringUtils.generateRandomString(6);
    String extension = fileNameAndExtension.extension;
    String newFileName = '${fileNameAndExtension.fileName}_${Platform.operatingSystem}_${randomPart}_$timestamp$extension';
    return newFileName;
  }

  Future<String?> uploadFileByS3SignedUrl(String outputFilepath, String endpointUrl, {String? filename}) async {
    try {
      String uploadFilename = (filename != null && filename.isNotEmpty) ? filename : getS3FilenameFromPath(outputFilepath);
      String contentType = FileUtils.getContentTypeFromFilePath(outputFilepath);
      final uploadResponse = await DI().resolve<ApiClient>().put(endpointUrl, body: {
        "filename": uploadFilename,
        "mimetype": contentType,
      });
      File rawFile = File(outputFilepath);
      List<int> fileBytes = await rawFile.readAsBytes();
      final Map<String, dynamic>? uploadResponseData = uploadResponse?["data"];
      String putSignedUrl = uploadResponseData?["url"] as String;
      await AwsApiClient.put(putSignedUrl, fileBytes, contentType);
      return uploadResponseData?["path"] as String;
    } catch (e) {
      logger.e("Failed to upload file: $outputFilepath to $endpointUrl:  ${e.toString()}");
      return null;
    }
  }
}

class AwsException implements Exception {
  final String error;
  final int? statusCode;
  final String? filePath;
  AwsException({required this.error, this.statusCode, this.filePath});

  @override
  String toString() {
    return 'AwsException{message: $error, statusCode: $statusCode}';
  }
}

sealed class Result {
  const Result();
}

final class Success extends Result {
  final String value;
  const Success(this.value);
}

final class Error extends Result {
  final String error;
  const Error(this.error);
}
