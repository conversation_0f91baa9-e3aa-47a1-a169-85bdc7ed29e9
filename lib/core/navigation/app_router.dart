import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:melodyze/core/navigation/guards/auth_guard.dart';
import 'package:melodyze/core/navigation/guards/force_update_guard.dart';
import 'package:melodyze/core/navigation/guards/preference_guard.dart';
import 'package:melodyze/core/navigation/route_module.dart';
import 'package:melodyze/core/ui/molecules/lyric_viewer/model/lyrics_data.dart';
import 'package:melodyze/modules/app_update/models/app_version_config.dart';
import 'package:melodyze/modules/app_update/routes/force_update_route.dart';
import 'package:melodyze/modules/app_update/screens/force_update_screen.dart';
import 'package:melodyze/modules/auth/routes/auth_routes.dart';
import 'package:melodyze/modules/dashboard/dashboard_screen.dart';
import 'package:melodyze/modules/feed/feed_screen.dart';
import 'package:melodyze/modules/feed/routes/feed_routes.dart';
import 'package:melodyze/modules/home/<USER>';
import 'package:melodyze/modules/home/<USER>/song_model/song_model.dart';
import 'package:melodyze/modules/home/<USER>/home_routes.dart';
import 'package:melodyze/modules/login/login_screen.dart';
import 'package:melodyze/modules/preference/preference_screen.dart';
import 'package:melodyze/modules/preference/routes/preference_routes.dart';
import 'package:melodyze/modules/profile/model/recording_model/recording_model.dart';
import 'package:melodyze/modules/profile/profile_screen.dart';
import 'package:melodyze/modules/profile/routes/profile_routes.dart';
import 'package:melodyze/modules/recording/cubit/download_wav_file_cubit.dart';
import 'package:melodyze/modules/recording/recording_screen.dart';
import 'package:melodyze/modules/recording/routes/recording_route_module.dart';
import 'package:melodyze/modules/save_and_upload/routes/save_and_upload_route_module.dart';
import 'package:melodyze/modules/save_and_upload/save_and_upload_screen.dart';
import 'package:melodyze/modules/search/routes/search_route_module.dart';
import 'package:melodyze/modules/search/search_screen.dart';
import 'package:melodyze/modules/share/share_data_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/audio_player_bloc/audio_player_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/juce_mix_player/juce_mix_bloc.dart';
import 'package:melodyze/modules/song_personalization/bloc/song_personalization_bloc.dart';
import 'package:melodyze/modules/song_personalization/routes/song_personalization_route_module.dart';
import 'package:melodyze/modules/song_personalization/song_personalization_screen.dart';
import 'package:melodyze/modules/video_player/routes/video_player_route_module.dart';
import 'package:melodyze/modules/video_player/video_player_full_screen.dart';
import 'package:melodyze/modules/video_player/video_player_reel_screen.dart';
import 'package:melodyze/modules/vocal_filters/bloc/vocal_filters_bloc.dart';
import 'package:melodyze/modules/vocal_filters/routes/song_personalization_route_module.dart';
import 'package:melodyze/modules/vocal_filters/vocal_filters_screen.dart';
import 'package:collection/collection.dart';

part 'app_router.gr.dart';

@AutoRouterConfig()
class AppRouter extends RootStackRouter {
  final AuthGuard _authGuard;
  final ForceUpdateGuard _forceUpdateGuard;
  final PreferenceGuard _preferenceGuard;

  AppRouter({
    required AuthGuard authGuard,
    required ForceUpdateGuard forceUpdateGuard,
    required PreferenceGuard preferenceGuard,
  })  : _authGuard = authGuard,
        _forceUpdateGuard = forceUpdateGuard,
        _preferenceGuard = preferenceGuard;

  final List<RouteModule> _preDashboardScreen = [
    AuthRoutesModule(),
    PreferenceRoutesModule(),
    ForceUpdateRoutesModule(),
  ];

  @override
  List<AutoRoute> get routes => [
        // Root route
        AutoRoute(
          path: '/',
          page: DashboardRoute.page,
          guards: [_forceUpdateGuard, _authGuard, _preferenceGuard],
          initial: true,
          children: [
            ...HomeRoutesModule().routes,
            ...FeedRoutesModule().routes,
            ...ProfileRoutesModule().routes,
            ...SearchRoutesModule().routes,
            ...VideoPlayerRoutesModule().routes,
          ],
        ),

        ..._addPreDashboardRoutes(),

        ...SongPersonalizationRoutesModule().routes,
        ...RecordingRoutesModule().routes,
        ...VocalFiltersRoutesModule().routes,
        // ...MasterFiltersRoutesModule().routes,
        ...SaveAndUploadRoutesModule().routes,

        // Fallback route for 404
        // AutoRoute(
        //   path: '*',
        //   page: NotFoundRoute.page,
        // ),
      ];

  List<AutoRoute> _addPreDashboardRoutes() {
    final allRoutes = <AutoRoute>[];
    for (final module in _preDashboardScreen) {
      allRoutes.addAll(module.routes);
    }
    return allRoutes;
  }
}
