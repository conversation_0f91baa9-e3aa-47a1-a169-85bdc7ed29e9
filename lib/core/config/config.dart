class Config {
  Config._();

  static const oauth2ClientId = '959993156379-61gprfe4ogt9j0ou88tm2m7etta18ga4.apps.googleusercontent.com';
  static const apiLimit = 100;
  static const keyMap = {
    "A_sharp": "A# / Bb",
    "C_sharp": "C# / Db",
    "D_sharp": "D# / Eb",
    "F_sharp": "F# / Gb",
    "G_sharp": "G# / Ab",
  };
  static const keyMapShort = {
    "A_sharp": "A#",
    "C_sharp": "C#",
    "D_sharp": "D#",
    "F_sharp": "F#",
    "G_sharp": "G#",
  };
}

class AwsConfig {
  AwsConfig._();
  // static const awsRegion = "ap-south-1";
}

class Endpoints {
  Endpoints._();

  //v1
  static const login = '/auth/v1/login';
  static const savePreferences = '/user/v1/save_preferences';
  static const deleteRecording = '/user/v1/delete_recording';
  static const getProfile = '/user/v1/get_profile';
  static const deleteAccount = '/user/v1/delete';
  static const updateFcmToken = '/user/v1/fcm_devices';
  static const saveAsFinal = '/user/v1/save_as_final';

  // PUT signed url
  static const getUploadRawVocalCachedSignedUrl = '/utilities/v1/s3_presigned_url/raw_vocal_cache';
  static const getUploadFilteredVocalSignedUrl = '/utilities/v1/s3_presigned_url/filtered_raw_vocal_audio';
  static const getUploadFinalMixedAudioSignedUrl = '/utilities/v1/s3_presigned_url/final_mixed_audio';

  //v2
  static const getPreferences = '/user/v2/preferences';
  static const feedsV2 = '/user/v2/feeds';
  static const searchV2 = '/song/v2/search';
  static const homeFeedGrids = '/user/v2/home_feed_grids';
  static const homeFeedTiles = '/user/v2/home_feed_tiles';
  static const gridOrderSongs = '/user/v2/home_feed_grids/populate_songs';
  static const tileOrderSongs = '/user/v2/home_feed_tiles/populate_songs';
  static const getRecordings = '/user/v2/get_recordings';
  static const saveRecording = '/user/v3/save_recording';
  static const denoiseCloud = '/user/v2/denoise_raw_vocal';
  static const getMasterSongById = '/song/v2/master/';
  static const getAllAnnotation = '/song/v2/annotated/get_all';
  static const getAnnotatedWav = '/song/v2/annotated/tempo_changed_wav_path';
}
