import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_fonts.dart';
import 'package:melodyze/core/ui/tokens/asset_paths.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class CenterOriginHorizontalSlider extends StatefulWidget {
  final ValueChanged<int>? onChanged;
  final VoidCallback? onChangeStart;
  final ValueChanged<int>? onChangeEnd;
  final int range;
  final String suffixText;
  final double negativeButtonOffset;
  final double positiveButtonOffset;
  final int initialValue;

  const CenterOriginHorizontalSlider({
    super.key,
    this.onChanged,
    this.onChangeStart,
    this.onChangeEnd,
    this.range = 1000,
    this.suffixText = 'ms',
    this.negativeButtonOffset = 24,
    this.positiveButtonOffset = 24,
    this.initialValue = 0,
  }) : assert(range >= 0);

  @override
  State<CenterOriginHorizontalSlider> createState() => _CenterOriginHorizontalSliderState();
}

class _CenterOriginHorizontalSliderState extends State<CenterOriginHorizontalSlider> {
  int _value = 0;
  double _sliderWidth = 0.0;
  bool _isInZeroZone = false; // Track if we're in the zero zone for visual feedback
  static const int _zeroZoneThreshold = 15; // Zone around zero for enhanced UX
  static const int _snapDistance = 8; // Distance for immediate snap to zero

  @override
  void initState() {
    super.initState();
    _value = widget.initialValue;
  }

  @override
  void didUpdateWidget(covariant CenterOriginHorizontalSlider oldWidget) {
    if (oldWidget.range > widget.range) {
      _value = _roundToNearestMultipleOfFive(widget.range);
    }
    if (oldWidget.initialValue != widget.initialValue) {
      _value = widget.initialValue;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: EdgeInsets.only(bottom: 0, top: 12), // Increased gap for better spacing
          child: Text(
            'Latency: ${_value != 0 ? (_value > 0 ? '+' : '-') : ''}${_value.abs()}${widget.suffixText}',
            style: TextStyle(
              fontFamily: AppFonts.iceland,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.white,
            ),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(left: widget.negativeButtonOffset),
              child: IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () {
                  setState(() {
                    _value = _roundToNearestMultipleOfFive(_value - 5);
                  });
                  widget.onChanged?.call(_value);
                  widget.onChangeEnd?.call(_value);
                },
                icon: ImageLoader.fromAsset(AssetPaths.circleNegative),
              ),
            ),
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  _sliderWidth = constraints.maxWidth;
                  return GestureDetector(
                    onHorizontalDragStart: (details) {
                      widget.onChangeStart?.call();
                    },
                    onHorizontalDragUpdate: (details) {
                      setState(() {
                        double relativePosition = details.localPosition.dx / _sliderWidth;
                        int newValue = ((relativePosition - 0.5) * (widget.range * 2)).toInt();
                        newValue = newValue.clamp(-widget.range, widget.range);
                        newValue = _processValueForZero(newValue);
                        _value = _roundToNearestMultipleOfFive(newValue);
                      });
                      widget.onChanged?.call(_value);
                    },
                    onHorizontalDragEnd: (details) {
                      widget.onChangeEnd?.call(_value);
                    },
                    child: SizedBox(
                      height: 30, // Increased height for better gesture area
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          // Base line
                          Container(
                            height: 1.0,
                            width: _sliderWidth,
                            color: Colors.grey[800],
                          ),

                          // Negative progress
                          if (_value < 0)
                            Positioned(
                              left: _subSliderWidth() - ((_value.abs() / widget.range) * _subSliderWidth()),
                              child: Container(
                                height: 1.5,
                                width: (_value.abs() / widget.range) * _subSliderWidth(),
                                decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.magenta_50,
                                      AppColors.magenta_100,
                                    ],
                                  ),
                                ),
                              ),
                            ),

                          // Negative thumb tip
                          if (_value < 0)
                            Positioned(
                              left: _subSliderWidth() - ((_value.abs() / widget.range) * _subSliderWidth()) - 1,
                              child: Container(
                                width: 2,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(1),
                                ),
                              ),
                            ),

                          // Positive progress
                          if (_value > 0)
                            Positioned(
                              right: _subSliderWidth() - ((_value.abs() / widget.range) * _subSliderWidth()),
                              child: Container(
                                height: 1.5,
                                width: (_value.abs() / widget.range) * _subSliderWidth(),
                                decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.cyanA400_50,
                                      AppColors.cyanA400_100,
                                    ],
                                  ),
                                ),
                              ),
                            ),

                          // Positive thumb tip
                          if (_value > 0)
                            Positioned(
                              right: _subSliderWidth() - ((_value.abs() / widget.range) * _subSliderWidth()) - 1,
                              child: Container(
                                width: 2,
                                height: 8,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(1),
                                ),
                              ),
                            ),

                          // Enhanced center notch with visual feedback
                          Positioned(
                            child: GestureDetector(
                              onTap: () {
                                setState(() => _value = 0);
                                widget.onChanged?.call(0);
                                widget.onChangeEnd?.call(0);
                                HapticFeedback.mediumImpact();
                              },
                              child: Container(
                                width: 24, // Larger gesture area
                                height: 24, // Larger gesture area
                                color: Colors.transparent,
                                child: Center(
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 150),
                                    width: _isInZeroZone ? 3 : 1, // Wider when in zero zone
                                    height: _isInZeroZone ? 8 : 4, // Taller when in zero zone
                                    decoration: BoxDecoration(
                                      color: _isInZeroZone ? Colors.white : Colors.white70,
                                      borderRadius: BorderRadius.circular(2),
                                      boxShadow: _isInZeroZone
                                          ? [
                                              BoxShadow(
                                                color: Colors.white.withValues(alpha: 0.3),
                                                blurRadius: 2,
                                                spreadRadius: 1,
                                              )
                                            ]
                                          : [
                                              BoxShadow(
                                                blurRadius: 2,
                                                offset: const Offset(0, 1),
                                              )
                                            ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),

                          // Gesture area for negative thumb (when value is negative)
                          if (_value < 0)
                            Positioned(
                              left: _subSliderWidth() - ((_value.abs() / widget.range) * _subSliderWidth()) - 12,
                              child: GestureDetector(
                                onPanStart: (details) {
                                  widget.onChangeStart?.call();
                                },
                                onPanUpdate: (details) {
                                  final RenderBox renderBox = context.findRenderObject() as RenderBox;
                                  final localPosition = renderBox.globalToLocal(details.globalPosition);
                                  setState(() {
                                    double relativePosition = localPosition.dx / _sliderWidth;
                                    int newValue = ((relativePosition - 0.5) * (widget.range * 2)).toInt();
                                    newValue = newValue.clamp(-widget.range, widget.range);

                                    // Process value for zero detection and feedback
                                    newValue = _processValueForZero(newValue);

                                    _value = _roundToNearestMultipleOfFive(newValue);
                                  });
                                  widget.onChanged?.call(_value);
                                },
                                onPanEnd: (details) {
                                  widget.onChangeEnd?.call(_value);
                                },
                                child: Container(
                                  width: 12,
                                  height: 12,
                                  color: Colors.transparent,
                                ),
                              ),
                            ),

                          // Gesture area for positive thumb (when value is positive)
                          if (_value > 0)
                            Positioned(
                              right: _subSliderWidth() - ((_value.abs() / widget.range) * _subSliderWidth()) - 12,
                              child: GestureDetector(
                                onPanStart: (details) {
                                  widget.onChangeStart?.call();
                                },
                                onPanUpdate: (details) {
                                  final RenderBox renderBox = context.findRenderObject() as RenderBox;
                                  final localPosition = renderBox.globalToLocal(details.globalPosition);
                                  setState(() {
                                    double relativePosition = localPosition.dx / _sliderWidth;
                                    int newValue = ((relativePosition - 0.5) * (widget.range * 2)).toInt();
                                    newValue = newValue.clamp(-widget.range, widget.range);

                                    // Process value for zero detection and feedback
                                    newValue = _processValueForZero(newValue);

                                    _value = _roundToNearestMultipleOfFive(newValue);
                                  });
                                  widget.onChanged?.call(_value);
                                },
                                onPanEnd: (details) {
                                  widget.onChangeEnd?.call(_value);
                                },
                                child: Container(
                                  width: 24,
                                  height: 24,
                                  color: Colors.transparent,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            Padding(
              padding: EdgeInsets.only(right: widget.positiveButtonOffset),
              child: IconButton(
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: () {
                  setState(() {
                    _value = _roundToNearestMultipleOfFive(_value + 5);
                  });
                  widget.onChanged?.call(_value);
                  widget.onChangeEnd?.call(_value);
                },
                icon: ImageLoader.fromAsset(AssetPaths.circlePositive),
              ),
            ),
          ],
        ),
      ],
    );
  }

  double _subSliderWidth() {
    if (_sliderWidth <= 0) {
      return 0;
    }
    return (_sliderWidth / 2) - 1;
  }

  int _roundToNearestMultipleOfFive(int number) {
    return ((number ~/ 5) * 5).clamp(-widget.range, widget.range);
  }

  /// Enhanced zero detection with user-friendly feedback
  int _processValueForZero(int newValue) {
    bool wasInZeroZone = _isInZeroZone;
    _isInZeroZone = newValue.abs() <= _zeroZoneThreshold;

    // Provide haptic feedback when entering zero zone
    if (_isInZeroZone && !wasInZeroZone) {
      HapticFeedback.selectionClick();
    }

    // Snap to zero if very close
    if (newValue.abs() <= _snapDistance) {
      if (newValue != 0) {
        HapticFeedback.lightImpact();
      }
      return 0;
    }

    return newValue;
  }
}
