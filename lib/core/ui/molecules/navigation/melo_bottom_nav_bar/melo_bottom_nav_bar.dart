import 'package:flutter/material.dart';
import 'package:melodyze/core/ui/atom/app_gradient_container.dart';
import 'package:melodyze/core/ui/tokens/app_colors.dart';
import 'package:melodyze/core/ui/tokens/app_gradients.dart';
import 'package:melodyze/core/wrappers/analytics/user_event.dart';
import 'package:melodyze/core/wrappers/image_loader.dart';

class MeloBottomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final List<String> items;
  final ValueChanged<int>? onTap;
  const MeloBottomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.items,
    this.onTap,
  }) : assert(
          items.length > 1,
          'The length of items must be greater than 1',
        );

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8),
      child: AppGradientContainer(
        enableBlur: true,
        gradient: AppGradients.gradientBottomBar,
        borderGradient: AppGradients.gradientBottomBarBorder,
        borderRadius: BorderRadius.circular(36),
        child: Padding(
          padding: EdgeInsets.all(1.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              for (final item in items)
                _buildIcon(
                  item,
                  items.indexOf(item),
                  currentIndex,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget get _outerCircle {
    BoxShadow shadow(double blurRadius) {
      return BoxShadow(
        color: AppColors.pinkGlow,
        blurRadius: blurRadius,
      );
    }

    return Container(
      height: 52,
      width: 52,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.black,
        boxShadow: [shadow(0.12), shadow(0.25), shadow(0.87), shadow(1.75), shadow(3.0), shadow(5.24)],
      ),
    );
  }

  Widget _buildIcon(String icon, int index, int currentIndex) {
    final isCenter = index == (items.length ~/ 2);
    return GestureDetector(
      onTap: () {
        onTap?.call(index);
        _fireUserEvent(index);
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          if (isCenter) _outerCircle,
          AppGradientContainer(
            gradient: isCenter ? AppGradients.gradientBottomBarMiddleIcon : AppGradients.gradientPinkBackground,
            borderGradient: isCenter ? AppGradients.gradientBottomBarMiddleIcon : AppGradients.gradientPinkBorder,
            borderRadius: BorderRadius.circular(36),
            height: isCenter ? 52 : 32,
            width: isCenter ? 52 : 32,
            shape: BoxShape.circle,
            child: Padding(
              padding: EdgeInsets.all(isCenter ? 10 : 8),
              child: ImageLoader.fromAsset(
                icon,
                color: currentIndex == index ? Colors.white : AppColors.black6B6B6B,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _fireUserEvent(int currentIndex) {
    if (currentIndex == this.currentIndex) return;
    switch (currentIndex) {
      case 0:
        UserEvent.shared.home_loaded();
        break;
      case 1:
        UserEvent.shared.profile_loaded();
        break;
    }
  }
}
