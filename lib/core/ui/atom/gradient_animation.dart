import 'dart:ui' as ui;

import 'package:flutter/material.dart';

class RippleAnimation extends StatefulWidget {
  final double size;
  final Color primaryColor;
  final Color secondaryColor;
  final Widget? child;

  const RippleAnimation({
    super.key,
    this.size = 100.0,
    this.primaryColor = const Color(0xFFE040FB),
    this.secondaryColor = const Color(0xFF9C27B0),
    this.child,
  });

  @override
  State<RippleAnimation> createState() => _RippleAnimationState();
}

class _RippleAnimationState extends State<RippleAnimation> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2000),
    )..repeat();

    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ClipRect(
      child: Center(
        child: SizedBox(
          width: widget.size,
          height: widget.size,
          child: Stack(
            alignment: Alignment.center,
            children: [
              AnimatedBuilder(
                animation: _animation,
                builder: (context, child) {
                  return CustomPaint(
                    painter: RipplePainter(
                      progress: _animation.value,
                      primaryColor: widget.primaryColor,
                      secondaryColor: widget.secondaryColor,
                    ),
                    size: Size(widget.size, widget.size),
                  );
                },
              ),
              if (widget.child != null)
                BackdropFilter(
                  filter: ui.ImageFilter.blur(sigmaX: 3.0, sigmaY: 3.0),
                  child: widget.child!,
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class RipplePainter extends CustomPainter {
  final double progress;
  final Color primaryColor;
  final Color secondaryColor;

  RipplePainter({
    required this.progress,
    required this.primaryColor,
    required this.secondaryColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = (size.width - 16) / 2;

    // Draw multiple rings with different phases
    for (int i = 0; i < 4; i++) {
      final adjustedProgress = (progress + (i * 0.33)) % 1.0;
      final radius = maxRadius * adjustedProgress;

      // Calculate opacity based on progress with higher base opacity
      final opacity = ((1.0 - adjustedProgress) * 0.8 + 0.2).clamp(0.0, 1.0);

      // Draw blur effect
      final blurPaint = Paint()
        ..color = primaryColor.withOpacity(opacity * 0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 15.0
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15.0);

      canvas.drawCircle(center, radius, blurPaint);

      // Create more vibrant gradient
      final gradient = RadialGradient(
        colors: [
          primaryColor.withOpacity(opacity),
          secondaryColor.withOpacity(opacity * 0.7),
        ],
        stops: const [0.5, 1.0],
      );

      // Create shader for main ring
      final shader = gradient.createShader(
        Rect.fromCircle(center: center, radius: radius),
      );

      final ringPaint = Paint()
        ..shader = shader
        ..style = PaintingStyle.stroke
        ..strokeWidth = 8.0;

      canvas.drawCircle(center, radius, ringPaint);
    }

    // Add central glow
    final centerGlow = Paint()
      ..color = primaryColor.withOpacity(0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 20.0);

    canvas.drawCircle(center, maxRadius * 0.2, centerGlow);
  }

  @override
  bool shouldRepaint(RipplePainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}
